# Windows TUN API 参考文档

本文档提供了 `wintun-api` crate 的完整 API 参考。

## 📚 目录

- [核心类型](#核心类型)
- [适配器管理](#适配器管理)
- [会话管理](#会话管理)
- [数据包处理](#数据包处理)
- [错误处理](#错误处理)
- [常量和配置](#常量和配置)
- [示例代码](#示例代码)

## 🔧 核心类型

### `Adapter`

表示一个 TUN 虚拟网络适配器。

```rust
pub struct Adapter {
    // 私有字段
}

impl Adapter {
    /// 创建新的 TUN 适配器
    pub fn create(name: &str, tunnel_type: &str, guid: Option<Uuid>) -> Result<Self>
    
    /// 打开现有的 TUN 适配器
    pub fn open(name: &str) -> Result<Self>
    
    /// 获取适配器名称
    pub fn name(&self) -> &str
    
    /// 获取隧道类型
    pub fn tunnel_type(&self) -> &str
    
    /// 获取适配器 GUID
    pub fn guid(&self) -> Uuid
    
    /// 获取适配器 LUID
    pub fn luid(&self) -> NET_LUID
    
    /// 在此适配器上启动会话
    pub fn start_session(&self, capacity: u32) -> Result<Session>
    
    /// 删除适配器
    pub fn delete(self) -> Result<()>
    
    /// 检查适配器是否有效
    pub fn is_valid(&self) -> bool
}
```

### `AdapterBuilder`

用于创建 TUN 适配器的构建器模式。

```rust
pub struct AdapterBuilder {
    // 私有字段
}

impl AdapterBuilder {
    /// 创建新的适配器构建器
    pub fn new() -> Self
    
    /// 设置适配器名称
    pub fn name<S: Into<String>>(self, name: S) -> Self
    
    /// 设置隧道类型
    pub fn tunnel_type<S: Into<String>>(self, tunnel_type: S) -> Self
    
    /// 设置适配器 GUID
    pub fn guid(self, guid: Uuid) -> Self
    
    /// 构建适配器
    pub fn build(self) -> Result<Adapter>
}
```

### `Session`

表示在 TUN 适配器上的活动会话。

```rust
pub struct Session {
    // 私有字段
}

impl Session {
    /// 获取读取等待事件句柄
    pub fn read_wait_event(&self) -> HANDLE
    
    /// 分配发送数据包
    pub fn allocate_send_packet(&mut self, packet_size: u32) -> Result<SendPacket>
    
    /// 发送数据包
    pub fn send_packet(&mut self, packet: SendPacket) -> Result<()>
    
    /// 接收数据包
    pub fn receive_packet(&mut self) -> Result<Option<ReceivePacket>>
    
    /// 释放接收的数据包
    pub fn release_receive_packet(&mut self, packet: ReceivePacket) -> Result<()>
    
    /// 等待数据包变为可用
    pub fn wait_for_packets(&self, timeout: Option<Duration>) -> Result<bool>
    
    /// 检查会话是否活动
    pub fn is_active(&self) -> bool
    
    /// 获取环形缓冲区容量
    pub fn capacity(&self) -> u32
    
    /// 获取关联的适配器
    pub fn adapter(&self) -> &Adapter
    
    /// 结束会话
    pub fn end(mut self) -> Result<()>
}
```

## 📦 数据包处理

### `SendPacket`

表示用于发送的数据包。

```rust
pub struct SendPacket {
    // 私有字段
}

impl SendPacket {
    /// 获取数据包数据的可变切片
    pub fn data_mut(&mut self) -> Result<&mut [u8]>
    
    /// 获取数据包数据的切片
    pub fn data(&self) -> &[u8]
    
    /// 获取数据包大小
    pub fn size(&self) -> u32
}
```

### `ReceivePacket`

表示接收到的数据包。

```rust
pub struct ReceivePacket {
    // 私有字段
}

impl ReceivePacket {
    /// 获取数据包数据的切片
    pub fn data(&self) -> &[u8]
    
    /// 获取数据包大小
    pub fn size(&self) -> u32
}
```

## ❌ 错误处理

### `WintunError`

表示 Wintun API 操作中可能发生的错误。

```rust
pub enum WintunError {
    /// Windows API 错误
    WindowsApi(windows::core::Error),
    
    /// 无效参数
    InvalidParameter(String),
    
    /// 适配器未找到
    AdapterNotFound(String),
    
    /// 适配器已存在
    AdapterAlreadyExists(String),
    
    /// 会话未启动
    SessionNotStarted,
    
    /// 会话已启动
    SessionAlreadyStarted,
    
    /// 缓冲区溢出
    BufferOverflow,
    
    /// 没有更多项目
    NoMoreItems,
    
    /// 无效数据
    InvalidData,
    
    /// 句柄 EOF
    HandleEof,
    
    /// 资源不足
    InsufficientResources,
    
    /// 驱动程序未加载
    DriverNotLoaded,
    
    /// 访问被拒绝
    AccessDenied,
    
    /// 操作不支持
    NotSupported,
    
    /// 通用 I/O 错误
    Io(std::io::Error),
}

impl WintunError {
    /// 从 Windows 错误代码创建 WintunError
    pub fn from_win32_error(error_code: WIN32_ERROR) -> Self
    
    /// 获取 Windows 错误代码（如果是 Windows API 错误）
    pub fn win32_error_code(&self) -> Option<u32>
    
    /// 检查此错误是否表示应该重试操作
    pub fn should_retry(&self) -> bool
    
    /// 检查此错误是否可恢复
    pub fn is_recoverable(&self) -> bool
}

/// Wintun 操作的结果类型
pub type Result<T> = std::result::Result<T, WintunError>;
```

## 🔧 常量和配置

```rust
/// 最大池名称长度（包括零终止符）
pub const MAX_POOL_NAME_LENGTH: usize = 256;

/// 最小环形容量 (128 KiB)
pub const MIN_RING_CAPACITY: u32 = 0x20000;

/// 最大环形容量 (64 MiB)
pub const MAX_RING_CAPACITY: u32 = 0x4000000;

/// 最大 IP 数据包大小
pub const MAX_IP_PACKET_SIZE: u32 = 0xFFFF;
```

## 📝 日志记录

### `LogLevel`

日志级别枚举。

```rust
pub enum LogLevel {
    Info,
    Warn,
    Error,
}
```

### 日志记录函数

```rust
/// 日志记录回调函数类型
pub type LoggerCallback = Box<dyn Fn(LogLevel, u64, &str) + Send + Sync>;

/// 设置全局日志记录回调
/// 
/// # 安全性
/// 此函数不是线程安全的，应该只在初始化期间调用一次。
pub unsafe fn set_logger(logger: LoggerCallback);
```

## 🔧 实用函数

### 适配器管理

```rust
/// 枚举所有 TUN 适配器
pub fn enumerate_adapters() -> Result<Vec<String>>;

/// 如果没有适配器在使用，删除 TUN 驱动程序
pub fn delete_driver() -> Result<()>;

/// 获取正在运行的驱动程序版本
pub fn get_running_driver_version() -> Result<u32>;
```

## 💡 示例代码

### 基本适配器创建

```rust
use wintun_api::{AdapterBuilder, LogLevel};
use uuid::Uuid;

fn create_adapter_example() -> wintun_api::Result<()> {
    // 设置日志记录
    unsafe {
        wintun_api::set_logger(Box::new(|level, _timestamp, message| {
            println!("[{:?}] {}", level, message);
        }));
    }

    // 创建适配器
    let adapter = AdapterBuilder::new()
        .name("示例适配器")
        .tunnel_type("Wintun")
        .guid(Uuid::new_v4())
        .build()?;

    println!("创建适配器: {}", adapter.name());
    println!("GUID: {}", adapter.guid());
    
    Ok(())
}
```

### 数据包处理示例

```rust
use wintun_api::{Adapter, LogLevel};
use std::time::Duration;

fn packet_processing_example() -> wintun_api::Result<()> {
    // 打开适配器
    let adapter = Adapter::open("示例适配器")?;
    
    // 启动会话
    let mut session = adapter.start_session(262144)?; // 256KB 缓冲区
    
    // 发送数据包
    let mut send_packet = session.allocate_send_packet(64)?;
    let data = send_packet.data_mut()?;
    data.fill(0xAA); // 填充测试数据
    session.send_packet(send_packet)?;
    
    // 接收数据包
    loop {
        if let Some(recv_packet) = session.receive_packet()? {
            println!("接收到 {} 字节数据包", recv_packet.size());
            
            // 处理数据包数据
            let data = recv_packet.data();
            println!("数据: {:02X?}", &data[..std::cmp::min(16, data.len())]);
            
            // 释放数据包
            session.release_receive_packet(recv_packet)?;
        } else {
            // 等待更多数据包
            if !session.wait_for_packets(Some(Duration::from_millis(100)))? {
                break; // 超时
            }
        }
    }
    
    // 结束会话
    session.end()?;
    Ok(())
}
```

### 错误处理示例

```rust
use wintun_api::{WintunError, Result};

fn error_handling_example() {
    match create_adapter_example() {
        Ok(()) => println!("操作成功"),
        Err(WintunError::AdapterAlreadyExists(name)) => {
            println!("适配器 '{}' 已存在", name);
        }
        Err(WintunError::AccessDenied) => {
            println!("访问被拒绝，请以管理员身份运行");
        }
        Err(WintunError::DriverNotLoaded) => {
            println!("驱动程序未加载");
        }
        Err(e) if e.should_retry() => {
            println!("临时错误，可以重试: {}", e);
        }
        Err(e) => {
            println!("操作失败: {}", e);
        }
    }
}
```

## 🔍 最佳实践

### 1. 资源管理

```rust
// 使用 RAII 确保资源正确清理
{
    let adapter = Adapter::create("临时适配器", "Wintun", None)?;
    let mut session = adapter.start_session(262144)?;
    
    // 使用会话...
    
    // session 和 adapter 在作用域结束时自动清理
}
```

### 2. 错误处理

```rust
// 检查可重试的错误
fn robust_operation() -> Result<()> {
    for attempt in 1..=3 {
        match risky_operation() {
            Ok(result) => return Ok(result),
            Err(e) if e.should_retry() && attempt < 3 => {
                std::thread::sleep(Duration::from_millis(100));
                continue;
            }
            Err(e) => return Err(e),
        }
    }
    unreachable!()
}
```

### 3. 性能优化

```rust
// 使用适当的缓冲区大小
let capacity = if high_throughput {
    1024 * 1024  // 1MB for high throughput
} else {
    256 * 1024   // 256KB for normal use
};

let mut session = adapter.start_session(capacity)?;
```

---

这个 API 参考文档提供了使用 `wintun-api` crate 所需的所有信息。有关更多示例和详细说明，请参阅项目的其他文档文件。
