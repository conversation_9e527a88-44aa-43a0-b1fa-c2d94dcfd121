# Windows TUN Driver in Rust

A Windows TUN (Layer 3) virtual network adapter driver implementation using Rust and the [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) library. This project is inspired by [WireG<PERSON>'s wintun](https://github.com/WireGuard/wintun) driver.

## Project Structure

This project consists of two main crates:

- **`wintun-driver`** - Kernel-mode NDIS miniport driver
- **`wintun-api`** - User-space API for managing TUN adapters and sessions

## Features

- Layer 3 (IP) virtual network adapter
- High-performance ring buffer for packet transfer
- Support for IPv4 and IPv6 packets
- NDIS 6.x miniport driver model
- Safe Rust API for user-space applications
- Configurable ring buffer sizes
- Event-driven packet processing

## Prerequisites

### Build Requirements

1. **LLVM/Clang** for binding generation:

   ```powershell
   winget install -i LLVM.LLVM --version 17.0.6 --force
   ```

   Make sure to add LLVM to PATH during installation.

2. **Cargo Make** for build automation:

   ```powershell
   cargo install --locked cargo-make --no-default-features --features tls-native
   ```

3. **Windows Driver Kit (WDK)** - Enter an eWDK developer prompt before building.

### Runtime Requirements

- Windows 7, 8, 8.1, 10, or 11
- Administrator privileges for driver installation
- Test signing enabled for development: `bcdedit /set testsigning on`

## Building

### Building the Driver

**Note**: The driver implementation is currently a demonstration/template and requires additional work to be production-ready.

1. Open an eWDK developer prompt
2. Navigate to the `wintun-driver` directory
3. Build the driver:
   ```powershell
   cargo make
   ```

This will build the driver and create a signed driver package in `target/<profile>/package/`.

**Important**: The current driver implementation is incomplete and serves as a starting point. A production driver would need:

- Complete NDIS miniport implementation
- Proper packet handling
- Device I/O control interface
- Registry configuration
- Proper error handling and cleanup

### Building the API

```powershell
cd wintun-api
cargo build --release
```

### Building the Example

```powershell
cd examples/simple-tun
cargo build --release
```

## Usage

### Creating a TUN Adapter

```rust
use wintun_api::{AdapterBuilder, LogLevel};
use uuid::Uuid;

// Set up logging
unsafe {
    wintun_api::set_logger(Box::new(|level, _timestamp, message| {
        println!("[{:?}] {}", level, message);
    }));
}

// Create a new TUN adapter
let adapter = AdapterBuilder::new()
    .name("MyTunAdapter")
    .tunnel_type("Wintun")
    .guid(Uuid::new_v4())
    .build()?;

println!("Created adapter: {}", adapter.name());
```

### Starting a Session

```rust
// Start a session with 1MB ring buffer
let mut session = adapter.start_session(0x100000)?;

// Send a packet
let mut packet = session.allocate_send_packet(64)?;
packet.data_mut()?.fill(0xAA); // Fill with test data
session.send_packet(packet)?;

// Receive packets
while let Some(packet) = session.receive_packet()? {
    println!("Received packet of size {}", packet.size());
    // Process packet data...
    session.release_receive_packet(packet)?;
}
```

### Example Application

Run the example application:

```powershell
cd examples/simple-tun

# Create a new adapter
cargo run -- create --name "TestTun" --tunnel-type "Wintun"

# List adapters
cargo run -- list

# Start a session
cargo run -- session --name "TestTun" --capacity 262144 --duration 30

# Get driver version
cargo run -- version
```

## Architecture

### Driver Architecture

The kernel-mode driver (`wintun-driver`) implements:

- **NDIS Miniport Driver**: Registers with Windows networking stack
- **Adapter Management**: Creates and manages virtual network adapters
- **Ring Buffer**: High-performance shared memory for packet transfer
- **Packet Processing**: Handles packet transmission and reception

### API Architecture

The user-space API (`wintun-api`) provides:

- **Adapter Management**: Create, open, and delete TUN adapters
- **Session Management**: Start/stop sessions for packet processing
- **Ring Buffer Interface**: Safe access to shared memory buffers
- **Event Handling**: Asynchronous packet notification

### Ring Buffer Design

The ring buffer implementation provides:

- Lock-free atomic operations for high performance
- Power-of-2 sizing for efficient modulo operations
- Packet headers with size information
- Separate send and receive buffers

## API Reference

### Core Types

- `Adapter` - Represents a TUN adapter
- `Session` - Active session for packet processing
- `Packet` - Individual network packet
- `AdapterBuilder` - Builder pattern for adapter creation

### Key Functions

- `Adapter::create()` - Create new adapter
- `Adapter::open()` - Open existing adapter
- `Session::allocate_send_packet()` - Allocate packet for sending
- `Session::receive_packet()` - Receive incoming packet
- `Session::wait_for_packets()` - Wait for packet availability

## Development

### Testing

Run tests for the API:

```powershell
cd wintun-api
cargo test
```

### Debugging

1. Enable test signing: `bcdedit /set testsigning on`
2. Install the driver package
3. Use WinDbg or Visual Studio for kernel debugging
4. Check Windows Event Viewer for driver logs

## Limitations

This is a demonstration implementation with the following limitations:

- Simplified NDIS implementation (not production-ready)
- Limited error handling in some areas
- No advanced features like checksum offload
- Requires administrator privileges

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## References

- [Windows Driver Kit Documentation](https://docs.microsoft.com/en-us/windows-hardware/drivers/)
- [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs)
- [WireGuard wintun](https://github.com/WireGuard/wintun)
- [NDIS Miniport Drivers](https://docs.microsoft.com/en-us/windows-hardware/drivers/network/ndis-miniport-drivers2)

## Acknowledgments

- Microsoft for the windows-drivers-rs framework
- WireGuard team for the wintun reference implementation
- Rust community for excellent tooling and libraries
