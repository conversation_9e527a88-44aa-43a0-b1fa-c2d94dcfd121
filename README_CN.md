# Windows TUN 驱动程序 (Rust 实现)

一个使用 Rust 和 [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) 库实现的 Windows TUN (第三层) 虚拟网络适配器驱动程序。本项目参考了 [WireGuard 的 wintun](https://github.com/WireGuard/wintun) 驱动程序设计。

## 项目结构

本项目包含两个主要的 crate：

- **`wintun-driver`** - 内核模式 NDIS 微端口驱动程序
- **`wintun-api`** - 用户空间 API，用于管理 TUN 适配器和会话

## 功能特性

- 第三层 (IP) 虚拟网络适配器
- 高性能环形缓冲区用于数据包传输
- 支持 IPv4 和 IPv6 数据包
- NDIS 6.x 微端口驱动程序模型
- 安全的 Rust 用户空间 API
- 可配置的环形缓冲区大小
- 事件驱动的数据包处理

## 系统要求

### 构建要求

1. **LLVM/Clang** 用于绑定生成：

   ```powershell
   winget install -i LLVM.LLVM --version 17.0.6 --force
   ```

   安装时确保将 LLVM 添加到 PATH。

2. **Cargo Make** 用于构建自动化：

   ```powershell
   cargo install --locked cargo-make --no-default-features --features tls-native
   ```

3. **Windows 驱动程序工具包 (WDK)** - 构建前需要进入 eWDK 开发者命令提示符。

### 运行要求

- Windows 7, 8, 8.1, 10, 或 11
- 驱动程序安装需要管理员权限
- 开发时需要启用测试签名：`bcdedit /set testsigning on`

## 构建

### 构建驱动程序

**注意**: 当前的驱动程序实现是演示/模板，需要额外工作才能用于生产环境。

1. 打开 eWDK 开发者命令提示符
2. 导航到 `wintun-driver` 目录
3. 构建驱动程序：
   ```powershell
   cargo make
   ```

这将构建驱动程序并在 `target/<profile>/package/` 中创建签名的驱动程序包。

**重要提示**: 当前的驱动程序实现不完整，仅作为起点。生产级驱动程序需要：

- 完整的 NDIS 微端口实现
- 正确的数据包处理
- 设备 I/O 控制接口
- 注册表配置
- 适当的错误处理和清理

### 构建 API

```powershell
cd wintun-api
cargo build --release
```

### 构建示例

```powershell
cd examples/simple-tun
cargo build --release
```

## 使用方法

### 创建 TUN 适配器

```rust
use wintun_api::{AdapterBuilder, LogLevel};
use uuid::Uuid;

// 设置日志记录
unsafe {
    wintun_api::set_logger(Box::new(|level, _timestamp, message| {
        println!("[{:?}] {}", level, message);
    }));
}

// 创建新的 TUN 适配器
let adapter = AdapterBuilder::new()
    .name("MyTunAdapter")
    .tunnel_type("Wintun")
    .guid(Uuid::new_v4())
    .build()?;

println!("创建适配器: {}", adapter.name());
```

### 启动会话

```rust
// 使用 1MB 环形缓冲区启动会话
let mut session = adapter.start_session(0x100000)?;

// 发送数据包
let mut packet = session.allocate_send_packet(64)?;
packet.data_mut()?.fill(0xAA); // 填充测试数据
session.send_packet(packet)?;

// 接收数据包
while let Some(packet) = session.receive_packet()? {
    println!("接收到大小为 {} 的数据包", packet.size());
    // 处理数据包数据...
    session.release_receive_packet(packet)?;
}
```

### 示例应用程序

运行示例应用程序：

```powershell
cd examples/simple-tun

# 创建新适配器
cargo run -- create --name "TestTun" --tunnel-type "Wintun"

# 列出适配器
cargo run -- list

# 启动会话
cargo run -- session --name "TestTun" --capacity 262144 --duration 30

# 获取驱动程序版本
cargo run -- version
```

## 架构

### 驱动程序架构

内核模式驱动程序 (`wintun-driver`) 实现：

- **NDIS 微端口驱动程序**: 向 Windows 网络堆栈注册
- **适配器管理**: 创建和管理虚拟网络适配器
- **环形缓冲区**: 用于数据包传输的高性能共享内存
- **数据包处理**: 处理数据包传输和接收

### API 架构

用户空间 API (`wintun-api`) 提供：

- **适配器管理**: 创建、打开和删除 TUN 适配器
- **会话管理**: 启动/停止数据包处理会话
- **环形缓冲区接口**: 安全访问共享内存缓冲区
- **事件处理**: 异步数据包通知

### 环形缓冲区设计

环形缓冲区实现提供：

- 高性能的无锁原子操作
- 2 的幂大小以实现高效的模运算
- 带有大小信息的数据包头
- 独立的发送和接收缓冲区

## API 参考

### 核心类型

- `Adapter` - 表示 TUN 适配器
- `Session` - 数据包处理的活动会话
- `SendPacket` - 用于发送的网络数据包
- `ReceivePacket` - 接收到的网络数据包
- `AdapterBuilder` - 适配器创建的构建器模式

### 主要函数

- `Adapter::create()` - 创建新适配器
- `Adapter::open()` - 打开现有适配器
- `Session::allocate_send_packet()` - 分配发送数据包
- `Session::receive_packet()` - 接收传入数据包
- `Session::wait_for_packets()` - 等待数据包可用

## 开发

### 测试

运行 API 测试：

```powershell
cd wintun-api
cargo test
```

运行所有测试（除驱动程序外）：

```powershell
cargo test --workspace --exclude wintun-driver
```

### 调试

1. 启用测试签名：`bcdedit /set testsigning on`
2. 安装驱动程序包
3. 使用 WinDbg 或 Visual Studio 进行内核调试
4. 检查 Windows 事件查看器中的驱动程序日志

### 代码质量

检查代码格式：

```powershell
cargo fmt --all -- --check
```

运行 Clippy 检查：

```powershell
cargo clippy --workspace --exclude wintun-driver -- -D warnings
```

### 性能测试

环形缓冲区性能测试：

```powershell
cargo test --release -p wintun-api ring_buffer::tests::test_packet_operations
```

## 故障排除

### 常见问题

**Q: 构建时出现 "file lock" 错误**
A: 清理构建缓存：`cargo clean`，然后重新构建。

**Q: 驱动程序安装失败**
A: 确保：

- 以管理员身份运行
- 启用了测试签名
- 使用正确的 WDK 版本

**Q: 数据包传输不工作**
A: 当前实现是演示版本，需要完整的 NDIS 实现才能进行实际的网络传输。

### 日志记录

启用详细日志记录：

```rust
use wintun_api::{LogLevel, set_logger};

unsafe {
    set_logger(Box::new(|level, timestamp, message| {
        println!("[{:?}] {} - {}", level, timestamp, message);
    }));
}
```

### 性能优化建议

1. **环形缓冲区大小**: 使用 2 的幂大小以获得最佳性能
2. **批处理**: 批量处理多个数据包以减少系统调用
3. **内存对齐**: 确保数据包在缓存行边界对齐

## 限制

这是一个演示实现，具有以下限制：

- 简化的 NDIS 实现（非生产就绪）
- 某些区域的错误处理有限
- 没有校验和卸载等高级功能
- 需要管理员权限

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 进行更改
4. 如适用，添加测试
5. 提交拉取请求

## 许可证

本项目采用 MIT 许可证 - 详见 LICENSE 文件。

## 参考资料

- [Windows 驱动程序工具包文档](https://docs.microsoft.com/zh-cn/windows-hardware/drivers/)
- [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs)
- [WireGuard wintun](https://github.com/WireGuard/wintun)
- [NDIS 微端口驱动程序](https://docs.microsoft.com/zh-cn/windows-hardware/drivers/network/ndis-miniport-drivers2)

## 致谢

- Microsoft 提供的 windows-drivers-rs 框架
- WireGuard 团队提供的 wintun 参考实现
- Rust 社区提供的优秀工具和库
