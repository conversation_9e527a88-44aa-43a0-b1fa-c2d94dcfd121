# Windows TUN 驱动程序构建脚本
param(
    [switch]$Driver,
    [switch]$Api,
    [switch]$Example,
    [switch]$All,
    [switch]$Test,
    [switch]$Clean,
    [switch]$Release,
    [switch]$Help
)

function Show-Help {
    Write-Host "Windows TUN 驱动程序构建脚本" -ForegroundColor Green
    Write-Host ""
    Write-Host "用法: .\build.ps1 [选项]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -Driver     构建内核驱动程序 (需要 WDK 环境)"
    Write-Host "  -Api        构建用户空间 API"
    Write-Host "  -Example    构建示例应用程序"
    Write-Host "  -All        构建所有组件"
    Write-Host "  -Test       运行测试"
    Write-Host "  -Clean      清理构建输出"
    Write-Host "  -Release    发布模式构建 (默认为调试模式)"
    Write-Host "  -Help       显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\build.ps1 -Api -Test          # 构建 API 并运行测试"
    Write-Host "  .\build.ps1 -All -Release       # 发布模式构建所有组件"
    Write-Host "  .\build.ps1 -Driver             # 仅构建驱动程序"
}

function Test-Prerequisites {
    Write-Host "检查构建先决条件..." -ForegroundColor Blue
    
    # 检查 Rust
    try {
        $rustVersion = cargo --version
        Write-Host "✓ Rust: $rustVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ 错误: 未找到 Rust。请安装 Rust 工具链。" -ForegroundColor Red
        exit 1
    }
    
    # 检查 LLVM (如果构建驱动程序)
    if ($Driver -or $All) {
        try {
            $llvmVersion = clang --version | Select-Object -First 1
            Write-Host "✓ LLVM: $llvmVersion" -ForegroundColor Green
        } catch {
            Write-Host "✗ 警告: 未找到 LLVM。驱动程序构建可能失败。" -ForegroundColor Yellow
        }
        
        # 检查 WDK 环境
        if (-not $env:WindowsSdkDir) {
            Write-Host "✗ 警告: 未检测到 WDK 环境。请在 eWDK 命令提示符中运行。" -ForegroundColor Yellow
        } else {
            Write-Host "✓ WDK 环境已检测到" -ForegroundColor Green
        }
    }
}

function Invoke-Clean {
    Write-Host "清理构建输出..." -ForegroundColor Blue
    
    if (Test-Path "target") {
        Remove-Item -Recurse -Force "target"
        Write-Host "✓ 已清理 target 目录" -ForegroundColor Green
    }
    
    if (Test-Path "Cargo.lock") {
        Remove-Item -Force "Cargo.lock"
        Write-Host "✓ 已删除 Cargo.lock" -ForegroundColor Green
    }
}

function Build-Api {
    Write-Host "构建用户空间 API..." -ForegroundColor Blue
    
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "build", $buildMode, "-p", "wintun-api" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ API 构建成功" -ForegroundColor Green
    } else {
        Write-Host "✗ API 构建失败" -ForegroundColor Red
        exit 1
    }
}

function Build-Example {
    Write-Host "构建示例应用程序..." -ForegroundColor Blue
    
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "build", $buildMode, "-p", "simple-tun" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ 示例应用程序构建成功" -ForegroundColor Green
    } else {
        Write-Host "✗ 示例应用程序构建失败" -ForegroundColor Red
        exit 1
    }
}

function Build-Driver {
    Write-Host "构建内核驱动程序..." -ForegroundColor Blue
    
    if (-not $env:WindowsSdkDir) {
        Write-Host "✗ 错误: 需要 WDK 环境。请在 eWDK 命令提示符中运行。" -ForegroundColor Red
        exit 1
    }
    
    Push-Location "wintun-driver"
    
    try {
        # 检查是否安装了 cargo-make
        try {
            cargo make --version | Out-Null
        } catch {
            Write-Host "安装 cargo-make..." -ForegroundColor Yellow
            cargo install --locked cargo-make --no-default-features --features tls-native
        }
        
        $result = Start-Process -FilePath "cargo" -ArgumentList "make" -Wait -PassThru -NoNewWindow
        
        if ($result.ExitCode -eq 0) {
            Write-Host "✓ 驱动程序构建成功" -ForegroundColor Green
        } else {
            Write-Host "✗ 驱动程序构建失败" -ForegroundColor Red
            exit 1
        }
    } finally {
        Pop-Location
    }
}

function Invoke-Tests {
    Write-Host "运行测试..." -ForegroundColor Blue
    
    # 运行 API 测试
    $result = Start-Process -FilePath "cargo" -ArgumentList "test", "-p", "wintun-api" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ API 测试通过" -ForegroundColor Green
    } else {
        Write-Host "✗ API 测试失败" -ForegroundColor Red
        exit 1
    }
    
    # 运行示例程序测试
    Write-Host "测试示例程序..." -ForegroundColor Blue
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "run", $buildMode, "-p", "simple-tun", "--", "--help" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ 示例程序测试通过" -ForegroundColor Green
    } else {
        Write-Host "✗ 示例程序测试失败" -ForegroundColor Red
        exit 1
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "构建完成!" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步:" -ForegroundColor Yellow
    Write-Host "1. 查看生成的文件在 target/ 目录中"
    Write-Host "2. 运行示例: cargo run --release -p simple-tun -- --help"
    Write-Host "3. 阅读文档: README_CN.md 和 快速开始.md"
    Write-Host ""
    
    if ($Driver -or $All) {
        Write-Host "驱动程序安装:" -ForegroundColor Yellow
        Write-Host "1. 以管理员身份运行: bcdedit /set testsigning on"
        Write-Host "2. 重启计算机"
        Write-Host "3. 安装驱动程序包 (在 target/package/ 中)"
    }
}

# 主逻辑
if ($Help) {
    Show-Help
    exit 0
}

if (-not ($Driver -or $Api -or $Example -or $All -or $Test -or $Clean)) {
    Write-Host "错误: 请指定至少一个操作。使用 -Help 查看帮助。" -ForegroundColor Red
    exit 1
}

Test-Prerequisites

if ($Clean) {
    Invoke-Clean
}

if ($All) {
    Build-Api
    Build-Example
    Build-Driver
} else {
    if ($Api) { Build-Api }
    if ($Example) { Build-Example }
    if ($Driver) { Build-Driver }
}

if ($Test) {
    Invoke-Tests
}

Show-Summary
