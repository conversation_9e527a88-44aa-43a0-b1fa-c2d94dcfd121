# Windows TUN Driver Build Script
param(
    [switch]$Driver,
    [switch]$Api,
    [switch]$Example,
    [switch]$All,
    [switch]$Test,
    [switch]$Clean,
    [switch]$Release,
    [switch]$Help
)

function Show-Help {
    Write-Host "Windows TUN Driver Build Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\build_simple.ps1 [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Driver     Build kernel driver (requires WDK environment)"
    Write-Host "  -Api        Build user-space API"
    Write-Host "  -Example    Build example application"
    Write-Host "  -All        Build all components"
    Write-Host "  -Test       Run tests"
    Write-Host "  -Clean      Clean build output"
    Write-Host "  -Release    Release mode build (default is debug)"
    Write-Host "  -Help       Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build_simple.ps1 -Api -Test"
    Write-Host "  .\build_simple.ps1 -All -Release"
    Write-Host "  .\build_simple.ps1 -Driver"
}

function Test-Prerequisites {
    Write-Host "Checking prerequisites..." -ForegroundColor Blue
    
    # Check Rust
    try {
        $rustVersion = cargo --version
        Write-Host "✓ Rust: $rustVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ Error: Rust not found. Please install Rust toolchain." -ForegroundColor Red
        exit 1
    }
}

function Invoke-Clean {
    Write-Host "Cleaning build output..." -ForegroundColor Blue
    
    if (Test-Path "target") {
        Remove-Item -Recurse -Force "target"
        Write-Host "✓ Cleaned target directory" -ForegroundColor Green
    }
    
    if (Test-Path "Cargo.lock") {
        Remove-Item -Force "Cargo.lock"
        Write-Host "✓ Removed Cargo.lock" -ForegroundColor Green
    }
}

function Build-Api {
    Write-Host "Building user-space API..." -ForegroundColor Blue
    
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "build", $buildMode, "-p", "wintun-api" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ API build successful" -ForegroundColor Green
    } else {
        Write-Host "✗ API build failed" -ForegroundColor Red
        exit 1
    }
}

function Build-Example {
    Write-Host "Building example application..." -ForegroundColor Blue
    
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "build", $buildMode, "-p", "simple-tun" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ Example build successful" -ForegroundColor Green
    } else {
        Write-Host "✗ Example build failed" -ForegroundColor Red
        exit 1
    }
}

function Build-Driver {
    Write-Host "Building kernel driver..." -ForegroundColor Blue
    Write-Host "Note: Driver build requires WDK environment and is currently a framework implementation." -ForegroundColor Yellow
    
    # For now, just check syntax
    $result = Start-Process -FilePath "cargo" -ArgumentList "check", "-p", "wintun-driver" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ Driver syntax check successful" -ForegroundColor Green
    } else {
        Write-Host "✗ Driver syntax check failed" -ForegroundColor Red
        exit 1
    }
}

function Invoke-Tests {
    Write-Host "Running tests..." -ForegroundColor Blue
    
    # Run API tests
    $result = Start-Process -FilePath "cargo" -ArgumentList "test", "-p", "wintun-api" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ API tests passed" -ForegroundColor Green
    } else {
        Write-Host "✗ API tests failed" -ForegroundColor Red
        exit 1
    }
    
    # Test example program
    Write-Host "Testing example program..." -ForegroundColor Blue
    $buildMode = if ($Release) { "--release" } else { "" }
    
    $result = Start-Process -FilePath "cargo" -ArgumentList "run", $buildMode, "-p", "simple-tun", "--", "--help" -Wait -PassThru -NoNewWindow
    
    if ($result.ExitCode -eq 0) {
        Write-Host "✓ Example program test passed" -ForegroundColor Green
    } else {
        Write-Host "✗ Example program test failed" -ForegroundColor Red
        exit 1
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "Build completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Check generated files in target/ directory"
    Write-Host "2. Run example: cargo run --release -p simple-tun -- --help"
    Write-Host "3. Read documentation: README_CN.md and 快速开始.md"
    Write-Host ""
}

# Main logic
if ($Help) {
    Show-Help
    exit 0
}

if (-not ($Driver -or $Api -or $Example -or $All -or $Test -or $Clean)) {
    Write-Host "Error: Please specify at least one operation. Use -Help for help." -ForegroundColor Red
    exit 1
}

Test-Prerequisites

if ($Clean) {
    Invoke-Clean
}

if ($All) {
    Build-Api
    Build-Example
    Build-Driver
} else {
    if ($Api) { Build-Api }
    if ($Example) { Build-Example }
    if ($Driver) { Build-Driver }
}

if ($Test) {
    Invoke-Tests
}

Show-Summary
