use std::time::Duration;
use clap::{Parser, Subcommand};
use uuid::Uuid;
use wintun_api::{
    Adapter<PERSON>uilder, LogLevel, Result, WintunError,
    adapter::{enumerate_adapters, delete_driver, get_running_driver_version},
};

#[derive(Parser)]
#[command(name = "simple-tun")]
#[command(about = "A simple TUN interface example using wintun-api")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Create a new TUN adapter
    Create {
        /// Name of the adapter
        #[arg(short, long)]
        name: String,
        /// Tunnel type (default: Wintun)
        #[arg(short, long, default_value = "Wintun")]
        tunnel_type: String,
        /// GUID for the adapter (optional)
        #[arg(short, long)]
        guid: Option<String>,
    },
    /// List all TUN adapters
    List,
    /// Start a session on an adapter
    Session {
        /// Name of the adapter
        #[arg(short, long)]
        name: String,
        /// Ring buffer capacity (must be power of 2)
        #[arg(short, long, default_value = "262144")]
        capacity: u32,
        /// Duration to run the session in seconds
        #[arg(short, long, default_value = "30")]
        duration: u64,
    },
    /// Delete the driver
    DeleteDriver,
    /// Get driver version
    Version,
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();

    // Set up logging
    unsafe {
        wintun_api::set_logger(Box::new(|level, _timestamp, message| {
            match level {
                LogLevel::Info => log::info!("{}", message),
                LogLevel::Warn => log::warn!("{}", message),
                LogLevel::Error => log::error!("{}", message),
            }
        }));
    }

    let cli = Cli::parse();

    match cli.command {
        Commands::Create { name, tunnel_type, guid } => {
            let guid = if let Some(guid_str) = guid {
                Some(Uuid::parse_str(&guid_str)
                    .map_err(|e| WintunError::InvalidParameter(format!("Invalid GUID: {}", e)))?)
            } else {
                None
            };

            let adapter = AdapterBuilder::new()
                .name(name.clone())
                .tunnel_type(tunnel_type)
                .guid(guid.unwrap_or_else(Uuid::new_v4))
                .build()?;

            println!("Created adapter '{}' with GUID {}", name, adapter.guid());
            println!("Adapter LUID: {:?}", adapter.luid());
        }

        Commands::List => {
            let adapters = enumerate_adapters()?;
            if adapters.is_empty() {
                println!("No TUN adapters found.");
            } else {
                println!("TUN Adapters:");
                for adapter in adapters {
                    println!("  - {}", adapter);
                }
            }
        }

        Commands::Session { name, capacity, duration } => {
            println!("Opening adapter '{}'...", name);
            let adapter = wintun_api::Adapter::open(&name)?;
            
            println!("Starting session with capacity {}...", capacity);
            let mut session = adapter.start_session(capacity)?;
            
            println!("Session started. Running for {} seconds...", duration);
            
            // Simulate packet processing
            let start_time = std::time::Instant::now();
            let duration = Duration::from_secs(duration);
            
            while start_time.elapsed() < duration {
                // Try to receive packets
                if let Some(packet) = session.receive_packet()? {
                    println!("Received packet of size {}", packet.size());
                    
                    // Echo the packet back (in a real implementation)
                    // For now, just release it
                    session.release_receive_packet(packet)?;
                }
                
                // Wait for more packets or timeout
                if !session.wait_for_packets(Some(Duration::from_millis(100)))? {
                    // Timeout - send a test packet
                    if let Ok(mut packet) = session.allocate_send_packet(64) {
                        // Fill with test data
                        if let Ok(data) = packet.data_mut() {
                            data.fill(0xAA);
                        }
                        session.send_packet(packet)?;
                        println!("Sent test packet");
                    }
                }
            }
            
            println!("Session completed.");
            session.end()?;
        }

        Commands::DeleteDriver => {
            println!("Deleting driver...");
            delete_driver()?;
            println!("Driver deleted successfully.");
        }

        Commands::Version => {
            match get_running_driver_version() {
                Ok(version) => {
                    let major = (version >> 16) & 0xFFFF;
                    let minor = version & 0xFFFF;
                    println!("Driver version: {}.{}", major, minor);
                }
                Err(e) => {
                    println!("Failed to get driver version: {}", e);
                }
            }
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_adapter_creation() {
        let result = AdapterBuilder::new()
            .name("TestAdapter")
            .tunnel_type("TestTun")
            .build();
        
        // In a real environment with the driver loaded, this should succeed
        // For testing purposes, we just verify the API works
        match result {
            Ok(adapter) => {
                assert_eq!(adapter.name(), "TestAdapter");
                assert_eq!(adapter.tunnel_type(), "TestTun");
            }
            Err(_) => {
                // Expected when driver is not loaded
            }
        }
    }

    #[test]
    fn test_guid_parsing() {
        let guid_str = "550e8400-e29b-41d4-a716-************";
        let guid = Uuid::parse_str(guid_str).unwrap();
        assert_eq!(guid.to_string(), guid_str);
    }
}
