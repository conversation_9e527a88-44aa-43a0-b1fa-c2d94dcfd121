[package]
name = "wintun-api"
version = "0.1.0"
edition = "2021"
description = "User-space API for Windows TUN driver"

[dependencies]
windows = { workspace = true, features = [
    "Win32_Foundation",
    "Win32_System_IO",
    "Win32_System_LibraryLoader",
    "Win32_System_Threading",
    "Win32_NetworkManagement_Ndis",
    "Win32_Devices_DeviceAndDriverInstallation",
    "Win32_System_Registry",
    "Win32_Security",
] }
thiserror = "1.0"
uuid = { version = "1.0", features = ["v4"] }

[features]
default = []
