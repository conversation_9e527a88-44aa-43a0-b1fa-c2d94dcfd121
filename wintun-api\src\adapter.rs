use uuid::Uuid;

use crate::driver_interface::{AdapterInfo, DriverInterface};
use crate::error::{Result, WintunError};
use crate::session::Session;
use crate::{log, LogLevel, MAX_POOL_NAME_LENGTH};

// Define NET_LUID as a simple wrapper since it's not available in windows crate
#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy)]
pub struct NET_LUID {
    pub Value: u64,
}

/// Represents a TUN adapter
pub struct Adapter {
    name: String,
    tunnel_type: String,
    guid: Uuid,
    luid: NET_LUID,
    is_driver_created: bool, // 标记是否在驱动程序中实际创建
}

impl Adapter {
    /// Create a new TUN adapter
    pub fn create(name: &str, tunnel_type: &str, guid: Option<Uuid>) -> Result<Self> {
        if name.len() >= MAX_POOL_NAME_LENGTH {
            return Err(WintunError::InvalidParameter(
                "Adapter name too long".to_string(),
            ));
        }

        if tunnel_type.len() >= MAX_POOL_NAME_LENGTH {
            return Err(WintunError::InvalidParameter(
                "Tunnel type too long".to_string(),
            ));
        }

        let guid = guid.unwrap_or_else(Uuid::new_v4);

        log(
            LogLevel::Info,
            &format!("Creating adapter '{}' with type '{}'", name, tunnel_type),
        );

        // 尝试连接到驱动程序
        let driver = match DriverInterface::open() {
            Ok(driver) => driver,
            Err(WintunError::DriverNotLoaded) => {
                log(
                    LogLevel::Warn,
                    "Driver not loaded, creating adapter in simulation mode",
                );
                // 在驱动程序未加载时，创建模拟适配器
                return Ok(Self {
                    name: name.to_string(),
                    tunnel_type: tunnel_type.to_string(),
                    guid,
                    luid: NET_LUID { Value: 0 },
                    is_driver_created: false,
                });
            }
            Err(e) => return Err(e),
        };

        // 创建适配器
        let guid_bytes = guid.as_bytes();
        match driver.create_adapter(name, tunnel_type, guid_bytes) {
            Ok(()) => {
                log(LogLevel::Info, "Successfully created adapter in driver");
            }
            Err(WintunError::AdapterAlreadyExists(_)) => {
                log(LogLevel::Warn, "Adapter already exists, continuing");
            }
            Err(e) => {
                log(LogLevel::Error, &format!("Failed to create adapter: {}", e));
                return Err(e);
            }
        }

        // 获取适配器信息
        let adapter_info = match driver.get_adapter_info(guid_bytes) {
            Ok(info) => info,
            Err(e) => {
                log(
                    LogLevel::Warn,
                    &format!("Failed to get adapter info: {}, using defaults", e),
                );
                // 使用默认信息
                AdapterInfo {
                    name: [0; 256],
                    tunnel_type: [0; 256],
                    guid: *guid_bytes,
                    luid: 0,
                    is_running: 0,
                }
            }
        };

        Ok(Self {
            name: name.to_string(),
            tunnel_type: tunnel_type.to_string(),
            guid,
            luid: NET_LUID {
                Value: adapter_info.luid,
            },
            is_driver_created: true,
        })
    }

    /// Open an existing TUN adapter by name
    pub fn open(name: &str) -> Result<Self> {
        if name.len() >= MAX_POOL_NAME_LENGTH {
            return Err(WintunError::InvalidParameter(
                "Adapter name too long".to_string(),
            ));
        }

        log(LogLevel::Info, &format!("Opening adapter '{}'", name));

        // 尝试连接到驱动程序
        let driver = match DriverInterface::open() {
            Ok(driver) => driver,
            Err(WintunError::DriverNotLoaded) => {
                return Err(WintunError::AdapterNotFound(format!(
                    "Driver not loaded. Cannot open adapter '{}'",
                    name
                )));
            }
            Err(e) => return Err(e),
        };

        // 在实际实现中，这里需要枚举所有适配器来查找匹配的名称
        // 当前简化实现：假设适配器存在但无法通过名称查找
        // 这需要驱动程序支持按名称查找适配器的功能

        log(
            LogLevel::Warn,
            &format!(
                "Adapter enumeration by name not yet implemented. Cannot open '{}'",
                name
            ),
        );

        Err(WintunError::AdapterNotFound(format!(
            "Adapter '{}' not found. Use create() to create a new adapter or implement adapter enumeration.",
            name
        )))
    }

    /// Get the adapter name
    pub fn name(&self) -> &str {
        &self.name
    }

    /// Get the tunnel type
    pub fn tunnel_type(&self) -> &str {
        &self.tunnel_type
    }

    /// Get the adapter GUID
    pub fn guid(&self) -> Uuid {
        self.guid
    }

    /// Get the adapter LUID
    pub fn luid(&self) -> NET_LUID {
        self.luid
    }

    /// Start a session on this adapter
    pub fn start_session(&self, capacity: u32) -> Result<Session> {
        Session::new(self, capacity)
    }

    /// Delete the adapter
    pub fn delete(self) -> Result<()> {
        log(LogLevel::Info, &format!("Deleting adapter '{}'", self.name));

        // 尝试连接到驱动程序
        let driver = match DriverInterface::open() {
            Ok(driver) => driver,
            Err(WintunError::DriverNotLoaded) => {
                log(
                    LogLevel::Warn,
                    "Driver not loaded, adapter deletion skipped",
                );
                return Ok(()); // 在模拟模式下，删除操作总是成功
            }
            Err(e) => return Err(e),
        };

        // 删除适配器
        let guid_bytes = self.guid.as_bytes();
        match driver.delete_adapter(guid_bytes) {
            Ok(()) => {
                log(LogLevel::Info, "Successfully deleted adapter from driver");
                Ok(())
            }
            Err(WintunError::AdapterNotFound(_)) => {
                log(
                    LogLevel::Warn,
                    "Adapter not found in driver, considering as deleted",
                );
                Ok(()) // 适配器不存在也算删除成功
            }
            Err(e) => {
                log(LogLevel::Error, &format!("Failed to delete adapter: {}", e));
                Err(e)
            }
        }
    }

    /// Check if the adapter was created in the driver
    pub fn is_driver_created(&self) -> bool {
        self.is_driver_created
    }

    /// Check if the adapter is valid (has a valid LUID)
    pub fn is_valid(&self) -> bool {
        self.luid.Value != 0 || self.is_driver_created
    }
}

impl Drop for Adapter {
    fn drop(&mut self) {
        // 适配器本身不持有句柄，无需清理
        // 实际的资源清理在会话级别进行
    }
}

/// Builder for creating TUN adapters with custom configuration
pub struct AdapterBuilder {
    name: Option<String>,
    tunnel_type: Option<String>,
    guid: Option<Uuid>,
}

impl AdapterBuilder {
    /// Create a new adapter builder
    pub fn new() -> Self {
        Self {
            name: None,
            tunnel_type: None,
            guid: None,
        }
    }

    /// Set the adapter name
    pub fn name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }

    /// Set the tunnel type
    pub fn tunnel_type<S: Into<String>>(mut self, tunnel_type: S) -> Self {
        self.tunnel_type = Some(tunnel_type.into());
        self
    }

    /// Set the adapter GUID
    pub fn guid(mut self, guid: Uuid) -> Self {
        self.guid = Some(guid);
        self
    }

    /// Build the adapter
    pub fn build(self) -> Result<Adapter> {
        let name = self
            .name
            .ok_or_else(|| WintunError::InvalidParameter("Adapter name is required".to_string()))?;

        let tunnel_type = self.tunnel_type.unwrap_or_else(|| "Wintun".to_string());

        Adapter::create(&name, &tunnel_type, self.guid)
    }
}

impl Default for AdapterBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Enumerate all TUN adapters
pub fn enumerate_adapters() -> Result<Vec<String>> {
    log(LogLevel::Info, "Enumerating adapters");

    // In a real implementation, this would enumerate actual adapters
    // For now, return an empty list
    Ok(Vec::new())
}

/// Delete the TUN driver if no adapters are using it
pub fn delete_driver() -> Result<()> {
    log(LogLevel::Info, "Deleting driver");

    // In a real implementation, this would unload the driver
    Ok(())
}

/// Get the running driver version
pub fn get_running_driver_version() -> Result<u32> {
    // In a real implementation, this would query the driver version
    Ok(0x00010000) // Version 1.0.0.0
}
