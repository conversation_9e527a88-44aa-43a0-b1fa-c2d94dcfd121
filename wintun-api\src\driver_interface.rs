use std::ffi::OsString;
use std::os::windows::ffi::OsStrExt;
use windows::Win32::Foundation::{<PERSON><PERSON>and<PERSON>, HANDLE, INVALID_HANDLE_VALUE};
use windows::Win32::Storage::FileSystem::{
    CreateFileW, FILE_ATTRIBUTE_NORMAL, FILE_SHARE_READ, FILE_SHARE_WRITE, OPEN_EXISTING,
};
use windows::Win32::System::IO::DeviceIoControl;

// 定义访问权限常量
const GENERIC_READ: u32 = 0x80000000;
const GENERIC_WRITE: u32 = 0x40000000;

use crate::error::{Result, WintunError};

/// IOCTL 控制代码（与驱动程序中的定义匹配）
const IOCTL_WINTUN_CREATE_ADAPTER: u32 = 0x220000;
const IOCTL_WINTUN_DELETE_ADAPTER: u32 = 0x220004;
const IOCTL_WINTUN_GET_ADAPTER_INFO: u32 = 0x220008;
const IOCTL_WINTUN_START_SESSION: u32 = 0x22000C;
const IOCTL_WINTUN_END_SESSION: u32 = 0x220010;
const IOCTL_WINTUN_GET_RING_BUFFER: u32 = 0x220014;
const IOCTL_WINTUN_SET_ADAPTER_STATE: u32 = 0x220018;

/// 驱动程序接口
pub struct DriverInterface {
    device_handle: HANDLE,
}

impl DriverInterface {
    /// 打开驱动程序设备
    pub fn open() -> Result<Self> {
        let device_path = r"\\.\WintunDriver";
        let device_path_wide: Vec<u16> = OsString::from(device_path)
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();

        let device_handle = unsafe {
            CreateFileW(
                windows::core::PCWSTR(device_path_wide.as_ptr()),
                GENERIC_READ | GENERIC_WRITE,
                FILE_SHARE_READ | FILE_SHARE_WRITE,
                None,
                OPEN_EXISTING,
                FILE_ATTRIBUTE_NORMAL,
                None,
            )
        };

        match device_handle {
            Ok(handle) if handle != INVALID_HANDLE_VALUE => Ok(Self {
                device_handle: handle,
            }),
            _ => Err(WintunError::DriverNotLoaded),
        }
    }

    /// 创建适配器
    pub fn create_adapter(&self, name: &str, tunnel_type: &str, guid: &[u8; 16]) -> Result<()> {
        let mut request = CreateAdapterRequest {
            name: [0; 256],
            tunnel_type: [0; 256],
            guid: *guid,
        };

        // 转换名称为宽字符
        let name_wide: Vec<u16> = OsString::from(name).encode_wide().collect();
        let name_len = std::cmp::min(name_wide.len(), request.name.len() - 1);
        request.name[..name_len].copy_from_slice(&name_wide[..name_len]);

        let tunnel_type_wide: Vec<u16> = OsString::from(tunnel_type).encode_wide().collect();
        let tunnel_type_len = std::cmp::min(tunnel_type_wide.len(), request.tunnel_type.len() - 1);
        request.tunnel_type[..tunnel_type_len]
            .copy_from_slice(&tunnel_type_wide[..tunnel_type_len]);

        self.send_ioctl(
            IOCTL_WINTUN_CREATE_ADAPTER,
            &request as *const _ as *const u8,
            std::mem::size_of::<CreateAdapterRequest>(),
            std::ptr::null_mut(),
            0,
        )?;

        Ok(())
    }

    /// 删除适配器
    pub fn delete_adapter(&self, guid: &[u8; 16]) -> Result<()> {
        self.send_ioctl(
            IOCTL_WINTUN_DELETE_ADAPTER,
            guid.as_ptr(),
            16,
            std::ptr::null_mut(),
            0,
        )?;

        Ok(())
    }

    /// 获取适配器信息
    pub fn get_adapter_info(&self, guid: &[u8; 16]) -> Result<AdapterInfo> {
        let mut adapter_info = AdapterInfo {
            name: [0; 256],
            tunnel_type: [0; 256],
            guid: *guid,
            luid: 0,
            is_running: 0,
        };

        self.send_ioctl(
            IOCTL_WINTUN_GET_ADAPTER_INFO,
            guid.as_ptr(),
            16,
            &mut adapter_info as *mut _ as *mut u8,
            std::mem::size_of::<AdapterInfo>(),
        )?;

        Ok(adapter_info)
    }

    /// 启动会话
    pub fn start_session(
        &self,
        adapter_guid: &[u8; 16],
        ring_capacity: u32,
    ) -> Result<SessionInfo> {
        let request = StartSessionRequest {
            adapter_guid: *adapter_guid,
            ring_capacity,
        };

        let mut session_info = SessionInfo {
            session_id: 0,
            send_ring_handle: 0,
            receive_ring_handle: 0,
            read_event_handle: 0,
        };

        self.send_ioctl(
            IOCTL_WINTUN_START_SESSION,
            &request as *const _ as *const u8,
            std::mem::size_of::<StartSessionRequest>(),
            &mut session_info as *mut _ as *mut u8,
            std::mem::size_of::<SessionInfo>(),
        )?;

        Ok(session_info)
    }

    /// 结束会话
    pub fn end_session(&self, session_id: u64) -> Result<()> {
        self.send_ioctl(
            IOCTL_WINTUN_END_SESSION,
            &session_id as *const _ as *const u8,
            8,
            std::ptr::null_mut(),
            0,
        )?;

        Ok(())
    }

    /// 设置适配器状态
    pub fn set_adapter_state(&self, guid: &[u8; 16], is_running: bool) -> Result<()> {
        let request = SetAdapterStateRequest {
            adapter_guid: *guid,
            is_running: if is_running { 1 } else { 0 },
        };

        self.send_ioctl(
            IOCTL_WINTUN_SET_ADAPTER_STATE,
            &request as *const _ as *const u8,
            std::mem::size_of::<SetAdapterStateRequest>(),
            std::ptr::null_mut(),
            0,
        )?;

        Ok(())
    }

    /// 发送 IOCTL 请求
    fn send_ioctl(
        &self,
        control_code: u32,
        input_buffer: *const u8,
        input_size: usize,
        output_buffer: *mut u8,
        output_size: usize,
    ) -> Result<usize> {
        let mut bytes_returned = 0u32;

        let success = unsafe {
            DeviceIoControl(
                self.device_handle,
                control_code,
                Some(input_buffer as *const std::ffi::c_void),
                input_size as u32,
                Some(output_buffer as *mut std::ffi::c_void),
                output_size as u32,
                Some(&mut bytes_returned),
                None,
            )
        };

        match success {
            Ok(_) => Ok(bytes_returned as usize),
            Err(e) => Err(WintunError::WindowsApi(e)),
        }
    }
}

impl Drop for DriverInterface {
    fn drop(&mut self) {
        if self.device_handle != INVALID_HANDLE_VALUE {
            unsafe {
                let _ = CloseHandle(self.device_handle);
            }
        }
    }
}

// 数据结构（与驱动程序中的定义匹配）
#[repr(C)]
struct CreateAdapterRequest {
    name: [u16; 256],
    tunnel_type: [u16; 256],
    guid: [u8; 16],
}

#[repr(C)]
pub struct AdapterInfo {
    pub name: [u16; 256],
    pub tunnel_type: [u16; 256],
    pub guid: [u8; 16],
    pub luid: u64,
    pub is_running: u32,
}

#[repr(C)]
struct StartSessionRequest {
    adapter_guid: [u8; 16],
    ring_capacity: u32,
}

#[repr(C)]
pub struct SessionInfo {
    pub session_id: u64,
    pub send_ring_handle: u64,
    pub receive_ring_handle: u64,
    pub read_event_handle: u64,
}

#[repr(C)]
struct SetAdapterStateRequest {
    adapter_guid: [u8; 16],
    is_running: u32,
}
