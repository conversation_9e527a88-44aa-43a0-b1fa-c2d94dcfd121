use thiserror::Error;
use windows::Win32::Foundation::WIN32_ERROR;

/// Result type for Wintun operations
pub type Result<T> = std::result::Result<T, WintunError>;

/// Errors that can occur when using the Wintun API
#[derive(Error, Debug)]
pub enum WintunError {
    /// Windows API error
    #[error("Windows API error: {0}")]
    WindowsApi(#[from] windows::core::Error),

    /// Invalid parameter
    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),

    /// Adapter not found
    #[error("Adapter not found: {0}")]
    AdapterNotFound(String),

    /// Adapter already exists
    #[error("Adapter already exists: {0}")]
    AdapterAlreadyExists(String),

    /// Session not started
    #[error("Session not started")]
    SessionNotStarted,

    /// Session already started
    #[error("Session already started")]
    SessionAlreadyStarted,

    /// Buffer overflow
    #[error("Buffer overflow")]
    BufferOverflow,

    /// No more items
    #[error("No more items")]
    NoMoreItems,

    /// Invalid data
    #[error("Invalid data")]
    InvalidData,

    /// Handle EOF
    #[error("Handle EOF")]
    HandleEof,

    /// Insufficient resources
    #[error("Insufficient resources")]
    InsufficientResources,

    /// Driver not loaded
    #[error("Driver not loaded")]
    DriverNotLoaded,

    /// Access denied
    #[error("Access denied")]
    AccessDenied,

    /// Operation not supported
    #[error("Operation not supported")]
    NotSupported,

    /// Generic I/O error
    #[error("I/O error: {0}")]
    Io(#[from] std::io::Error),
}

impl WintunError {
    /// Create a WintunError from a Windows error code
    pub fn from_win32_error(error_code: WIN32_ERROR) -> Self {
        match error_code.0 {
            0x6 => WintunError::InvalidParameter("Invalid handle".to_string()),
            0x50 => WintunError::AdapterNotFound("File not found".to_string()),
            0xB7 => WintunError::AdapterAlreadyExists("Already exists".to_string()),
            0x103 => WintunError::NoMoreItems,
            0x10D => WintunError::InvalidData,
            0x26 => WintunError::HandleEof,
            0x8 => WintunError::InsufficientResources,
            0x5 => WintunError::AccessDenied,
            0x32 => WintunError::NotSupported,
            _ => WintunError::WindowsApi(windows::core::Error::from_win32()),
        }
    }

    /// Get the Windows error code if this is a Windows API error
    pub fn win32_error_code(&self) -> Option<u32> {
        match self {
            WintunError::WindowsApi(err) => Some(err.code().0 as u32),
            _ => None,
        }
    }

    /// Check if this error indicates that the operation should be retried
    pub fn should_retry(&self) -> bool {
        matches!(self, WintunError::BufferOverflow | WintunError::NoMoreItems)
    }

    /// Check if this error is recoverable
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            WintunError::BufferOverflow
                | WintunError::NoMoreItems
                | WintunError::SessionNotStarted
                | WintunError::InvalidParameter(_)
        )
    }
}

/// Convert a Windows GetLastError() result to WintunError
pub fn last_error() -> WintunError {
    let error_code = unsafe { windows::Win32::Foundation::GetLastError() };
    WintunError::from_win32_error(error_code)
}
