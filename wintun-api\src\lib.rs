//! Windows TUN Driver User-Space API
//!
//! This crate provides a safe Rust API for creating and managing TUN (Layer 3)
//! virtual network adapters on Windows using the wintun-driver.

use std::ffi::OsString;
use std::os::windows::ffi::{OsStrExt, OsStringExt};

pub mod adapter;
pub mod error;
pub mod ring_buffer;
pub mod session;

pub use adapter::{Adapter, AdapterBuilder};
pub use error::{Result, WintunError};
pub use session::{Session, SendPacket, ReceivePacket};

/// Maximum pool name length including zero terminator
pub const MAX_POOL_NAME_LENGTH: usize = 256;

/// Minimum ring capacity (128 KiB)
pub const MIN_RING_CAPACITY: u32 = 0x20000;

/// Maximum ring capacity (64 MiB)
pub const MAX_RING_CAPACITY: u32 = 0x4000000;

/// Maximum IP packet size
pub const MAX_IP_PACKET_SIZE: u32 = 0xFFFF;

/// Logger callback function type
pub type LoggerCallback = Box<dyn Fn(LogLevel, u64, &str) + Send + Sync>;

/// Log levels for the logger callback
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum LogLevel {
    Info,
    Warn,
    Error,
}

/// Global logger instance
static mut LOGGER: Option<LoggerCallback> = None;

/// Set the global logger callback
///
/// # Safety
/// This function is not thread-safe and should only be called once during initialization.
pub unsafe fn set_logger(logger: LoggerCallback) {
    LOGGER = Some(logger);
}

/// Internal logging function
pub(crate) fn log(level: LogLevel, message: &str) {
    unsafe {
        if let Some(ref logger) = LOGGER {
            let timestamp = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64
                / 100; // Convert to 100ns intervals

            logger(level, timestamp, message);
        }
    }
}

/// Convert a Rust string to a Windows wide string
pub(crate) fn to_wide_string(s: &str) -> Vec<u16> {
    OsString::from(s)
        .encode_wide()
        .chain(std::iter::once(0))
        .collect()
}

/// Convert a Windows wide string to a Rust string
pub(crate) fn from_wide_string(wide: &[u16]) -> String {
    let end = wide.iter().position(|&c| c == 0).unwrap_or(wide.len());
    OsString::from_wide(&wide[..end])
        .to_string_lossy()
        .into_owned()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_wide_string_conversion() {
        let original = "Test Adapter";
        let wide = to_wide_string(original);
        let converted = from_wide_string(&wide);
        assert_eq!(original, converted);
    }

    #[test]
    fn test_constants() {
        assert!(MIN_RING_CAPACITY < MAX_RING_CAPACITY);
        assert!(MIN_RING_CAPACITY.is_power_of_two());
        assert!(MAX_RING_CAPACITY.is_power_of_two());
    }
}
