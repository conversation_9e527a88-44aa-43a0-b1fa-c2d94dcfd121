use std::sync::atomic::{AtomicU32, Ordering};
use std::alloc::{alloc, dealloc, Layout};
use std::ptr::NonNull;

use crate::error::{WintunError, Result};

/// Ring buffer for high-performance packet transfer
pub struct RingBuffer {
    buffer: NonNull<u8>,
    capacity: u32,
    head: AtomicU32,
    tail: AtomicU32,
    layout: Layout,
}

/// Packet header in ring buffer
#[repr(C, packed)]
struct PacketHeader {
    size: u32,
    reserved: u32,
}

impl RingBuffer {
    /// Create a new ring buffer with specified capacity
    pub fn new(capacity: u32) -> Result<Self> {
        if capacity == 0 || !capacity.is_power_of_two() {
            return Err(WintunError::InvalidParameter(
                "Capacity must be a power of two".to_string(),
            ));
        }

        let layout = Layout::from_size_align(capacity as usize, 4096)
            .map_err(|_| WintunError::InvalidParameter("Invalid layout".to_string()))?;

        let buffer = unsafe {
            let ptr = alloc(layout);
            if ptr.is_null() {
                return Err(WintunError::InsufficientResources);
            }
            
            // Zero the buffer
            std::ptr::write_bytes(ptr, 0, capacity as usize);
            
            NonNull::new_unchecked(ptr)
        };

        Ok(Self {
            buffer,
            capacity,
            head: AtomicU32::new(0),
            tail: AtomicU32::new(0),
            layout,
        })
    }

    /// Get available space for writing
    pub fn available_space(&self) -> u32 {
        let head = self.head.load(Ordering::Acquire);
        let tail = self.tail.load(Ordering::Acquire);
        
        if head >= tail {
            self.capacity - (head - tail) - 1
        } else {
            tail - head - 1
        }
    }

    /// Get available data for reading
    pub fn available_data(&self) -> u32 {
        let head = self.head.load(Ordering::Acquire);
        let tail = self.tail.load(Ordering::Acquire);
        
        if head >= tail {
            head - tail
        } else {
            self.capacity - (tail - head)
        }
    }

    /// Allocate space for a packet in the ring buffer
    pub fn allocate_packet(&self, packet_size: u32) -> Option<*mut u8> {
        let total_size = std::mem::size_of::<PacketHeader>() as u32 + packet_size;
        
        if self.available_space() < total_size {
            return None;
        }

        let head = self.head.load(Ordering::Acquire);
        let new_head = (head + total_size) & (self.capacity - 1);
        
        // Try to update head atomically
        if self.head.compare_exchange_weak(head, new_head, Ordering::Release, Ordering::Relaxed).is_err() {
            return None;
        }

        unsafe {
            let packet_ptr = self.buffer.as_ptr().add(head as usize);
            
            // Write packet header
            let header = PacketHeader {
                size: packet_size,
                reserved: 0,
            };
            std::ptr::write(packet_ptr as *mut PacketHeader, header);
            
            Some(packet_ptr.add(std::mem::size_of::<PacketHeader>()))
        }
    }

    /// Get next packet from ring buffer
    pub fn get_next_packet(&self) -> Option<(*const u8, u32)> {
        if self.available_data() < std::mem::size_of::<PacketHeader>() as u32 {
            return None;
        }

        let tail = self.tail.load(Ordering::Acquire);
        
        unsafe {
            let header_ptr = self.buffer.as_ptr().add(tail as usize) as *const PacketHeader;
            let header = std::ptr::read(header_ptr);
            
            let total_size = std::mem::size_of::<PacketHeader>() as u32 + header.size;
            
            if self.available_data() < total_size {
                return None;
            }

            let packet_ptr = self.buffer.as_ptr().add(
                (tail + std::mem::size_of::<PacketHeader>() as u32) as usize
            );
            
            Some((packet_ptr, header.size))
        }
    }

    /// Release a packet after processing
    pub fn release_packet(&self, packet_size: u32) {
        let total_size = std::mem::size_of::<PacketHeader>() as u32 + packet_size;
        let tail = self.tail.load(Ordering::Acquire);
        let new_tail = (tail + total_size) & (self.capacity - 1);
        self.tail.store(new_tail, Ordering::Release);
    }

    /// Check if ring buffer is empty
    pub fn is_empty(&self) -> bool {
        self.head.load(Ordering::Acquire) == self.tail.load(Ordering::Acquire)
    }

    /// Check if ring buffer is full
    pub fn is_full(&self) -> bool {
        self.available_space() == 0
    }

    /// Get the capacity of the ring buffer
    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    /// Reset the ring buffer
    pub fn reset(&self) {
        self.head.store(0, Ordering::Release);
        self.tail.store(0, Ordering::Release);
        
        unsafe {
            std::ptr::write_bytes(self.buffer.as_ptr(), 0, self.capacity as usize);
        }
    }
}

unsafe impl Send for RingBuffer {}
unsafe impl Sync for RingBuffer {}

impl Drop for RingBuffer {
    fn drop(&mut self) {
        unsafe {
            dealloc(self.buffer.as_ptr(), self.layout);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ring_buffer_creation() {
        let ring = RingBuffer::new(4096).unwrap();
        assert_eq!(ring.capacity(), 4096);
        assert!(ring.is_empty());
        assert!(!ring.is_full());
    }

    #[test]
    fn test_invalid_capacity() {
        assert!(RingBuffer::new(0).is_err());
        assert!(RingBuffer::new(1000).is_err()); // Not power of two
    }

    #[test]
    fn test_packet_allocation() {
        let ring = RingBuffer::new(4096).unwrap();
        let packet_ptr = ring.allocate_packet(100);
        assert!(packet_ptr.is_some());
        
        // Should have less available space now
        assert!(ring.available_space() < 4096 - 1);
    }

    #[test]
    fn test_packet_operations() {
        let ring = RingBuffer::new(4096).unwrap();
        
        // Allocate a packet
        let packet_ptr = ring.allocate_packet(100).unwrap();
        
        // Write some data
        unsafe {
            std::ptr::write_bytes(packet_ptr, 0xAA, 100);
        }
        
        // Try to get the packet
        if let Some((data_ptr, size)) = ring.get_next_packet() {
            assert_eq!(size, 100);
            
            // Verify data
            unsafe {
                let data = std::slice::from_raw_parts(data_ptr, size as usize);
                assert_eq!(data[0], 0xAA);
            }
            
            // Release the packet
            ring.release_packet(size);
        } else {
            panic!("Failed to get packet");
        }
        
        // Ring should be empty again
        assert!(ring.is_empty());
    }
}
