use std::sync::Arc;
use std::time::Duration;
use windows::Win32::Foundation::WAIT_EVENT;
use windows::Win32::Foundation::{CloseHandle, HANDLE, INVALID_HANDLE_VALUE};
use windows::Win32::System::Threading::{CreateEventW, WaitForSingleObject, INFINITE};

use crate::adapter::Adapter;
use crate::error::{Result, WintunError};
use crate::ring_buffer::RingBuffer;
use crate::{log, LogLevel, MAX_IP_PACKET_SIZE, MAX_RING_CAPACITY, MIN_RING_CAPACITY};

/// Represents a session on a TUN adapter
pub struct Session {
    adapter: Arc<Adapter>,
    capacity: u32,
    read_event: HANDLE,
    send_ring: RingBuffer,
    receive_ring: RingBuffer,
    is_active: bool,
}

impl Session {
    /// Create a new session
    pub(crate) fn new(adapter: &Adapter, capacity: u32) -> Result<Self> {
        if capacity < MIN_RING_CAPACITY || capacity > MAX_RING_CAPACITY {
            return Err(WintunError::InvalidParameter(format!(
                "Invalid ring capacity: {}",
                capacity
            )));
        }

        if !capacity.is_power_of_two() {
            return Err(WintunError::InvalidParameter(
                "Ring capacity must be a power of two".to_string(),
            ));
        }

        log(
            LogLevel::Info,
            &format!("Starting session with capacity {}", capacity),
        );

        // Create read event
        let read_event = unsafe {
            CreateEventW(None, false, false, None).map_err(|e| WintunError::WindowsApi(e))?
        };

        // Create ring buffers
        let send_ring = RingBuffer::new(capacity)?;
        let receive_ring = RingBuffer::new(capacity)?;

        // Create a copy of the adapter for the session
        let adapter_copy =
            Adapter::create(adapter.name(), adapter.tunnel_type(), Some(adapter.guid()))?;

        Ok(Self {
            adapter: Arc::new(adapter_copy),
            capacity,
            read_event,
            send_ring,
            receive_ring,
            is_active: true,
        })
    }

    /// Get the read wait event handle
    pub fn read_wait_event(&self) -> HANDLE {
        self.read_event
    }

    /// Allocate a packet for sending
    pub fn allocate_send_packet(&mut self, packet_size: u32) -> Result<Packet> {
        if !self.is_active {
            return Err(WintunError::SessionNotStarted);
        }

        if packet_size > MAX_IP_PACKET_SIZE {
            return Err(WintunError::InvalidParameter(format!(
                "Packet size {} exceeds maximum {}",
                packet_size, MAX_IP_PACKET_SIZE
            )));
        }

        let buffer = self
            .send_ring
            .allocate_packet(packet_size)
            .ok_or(WintunError::BufferOverflow)?;

        Ok(Packet {
            buffer,
            size: packet_size,
            session: self,
            is_send: true,
        })
    }

    /// Send a packet
    pub fn send_packet(&mut self, packet: Packet) -> Result<()> {
        if !self.is_active {
            return Err(WintunError::SessionNotStarted);
        }

        if !packet.is_send {
            return Err(WintunError::InvalidParameter(
                "Cannot send a receive packet".to_string(),
            ));
        }

        // In a real implementation, this would notify the driver
        log(
            LogLevel::Info,
            &format!("Sent packet of size {}", packet.size),
        );

        Ok(())
    }

    /// Receive a packet
    pub fn receive_packet(&mut self) -> Result<Option<Packet>> {
        if !self.is_active {
            return Err(WintunError::SessionNotStarted);
        }

        if let Some((buffer, size)) = self.receive_ring.get_next_packet() {
            Ok(Some(Packet {
                buffer,
                size,
                session: self,
                is_send: false,
            }))
        } else {
            Ok(None)
        }
    }

    /// Release a received packet
    pub fn release_receive_packet(&mut self, packet: Packet) -> Result<()> {
        if !self.is_active {
            return Err(WintunError::SessionNotStarted);
        }

        if packet.is_send {
            return Err(WintunError::InvalidParameter(
                "Cannot release a send packet".to_string(),
            ));
        }

        self.receive_ring.release_packet(packet.size);
        Ok(())
    }

    /// Wait for packets to become available
    pub fn wait_for_packets(&self, timeout: Option<Duration>) -> Result<bool> {
        if !self.is_active {
            return Err(WintunError::SessionNotStarted);
        }

        let timeout_ms = timeout.map(|d| d.as_millis() as u32).unwrap_or(INFINITE);

        let result = unsafe { WaitForSingleObject(self.read_event, timeout_ms) };

        Ok(result == WAIT_EVENT(0))
    }

    /// Check if the session is active
    pub fn is_active(&self) -> bool {
        self.is_active
    }

    /// Get the ring buffer capacity
    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    /// Get the associated adapter
    pub fn adapter(&self) -> &Adapter {
        &self.adapter
    }

    /// End the session
    pub fn end(mut self) -> Result<()> {
        self.is_active = false;
        log(LogLevel::Info, "Session ended");
        Ok(())
    }
}

impl Drop for Session {
    fn drop(&mut self) {
        self.is_active = false;
        if self.read_event != INVALID_HANDLE_VALUE {
            unsafe {
                let _ = CloseHandle(self.read_event);
            }
        }
    }
}

/// Represents a packet in the TUN interface
pub struct Packet<'a> {
    buffer: *const u8,
    size: u32,
    session: &'a Session,
    is_send: bool,
}

impl<'a> Packet<'a> {
    /// Get the packet data as a slice
    pub fn data(&self) -> &[u8] {
        unsafe { std::slice::from_raw_parts(self.buffer, self.size as usize) }
    }

    /// Get the packet data as a mutable slice (only for send packets)
    pub fn data_mut(&mut self) -> Result<&mut [u8]> {
        if !self.is_send {
            return Err(WintunError::InvalidParameter(
                "Cannot get mutable data for receive packet".to_string(),
            ));
        }

        Ok(unsafe { std::slice::from_raw_parts_mut(self.buffer as *mut u8, self.size as usize) })
    }

    /// Get the packet size
    pub fn size(&self) -> u32 {
        self.size
    }

    /// Check if this is a send packet
    pub fn is_send(&self) -> bool {
        self.is_send
    }

    /// Check if this is a receive packet
    pub fn is_receive(&self) -> bool {
        !self.is_send
    }
}
