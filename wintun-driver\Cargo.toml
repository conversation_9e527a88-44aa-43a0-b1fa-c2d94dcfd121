[package]
name = "wintun-driver"
version = "0.1.0"
edition = "2021"
description = "Windows TUN driver implementation using windows-drivers-rs"

[lib]
crate-type = ["cdylib"]

[dependencies]
wdk = { workspace = true }
wdk-sys = { workspace = true }
wdk-alloc = { workspace = true }
wdk-panic = { workspace = true }

[build-dependencies]
wdk-build = { workspace = true }

[package.metadata.wdk.driver-model]
driver-type = "KMDF"
kmdf-version-major = 1
target-kmdf-version-minor = 33
