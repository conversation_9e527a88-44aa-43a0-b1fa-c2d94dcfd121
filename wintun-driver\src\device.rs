use wdk_sys::{
    NTSTATUS, STATUS_SUCCESS, STATUS_UNSUCCESSFUL,
    WDFDRIVER, WDFDEVICE, PWDFDEVICE_INIT, WDFQUEUE,
    WDF_DEVICE_INIT, WDF_OBJECT_ATTRIBUTES, WDF_NO_OBJECT_ATTRIBUTES,
    WDF_IO_QUEUE_CONFIG, WDF_IO_QUEUE_DISPATCH_TYPE,
    WdfDeviceCreate, WdfDeviceInitSetDeviceType, WdfDeviceInitSetCharacteristics,
    WdfIoQueueCreate, WdfDeviceConfigureRequestDispatching,
    FILE_DEVICE_NETWORK, FILE_DEVICE_SECURE_OPEN,
    PWDF_OBJECT_ATTRIBUTES, PWDF_IO_QUEUE_CONFIG,
    WdfIoQueueDispatchParallel, WdfRequestTypeDeviceControl,
};

use crate::ndis::initialize_ndis_miniport;
use crate::ioctl::EvtIoDeviceControl;
use crate::registry::{read_driver_config, RegistryConfig};

/// Device context structure
#[repr(C)]
pub struct DeviceContext {
    pub miniport_handle: *mut core::ffi::c_void,
    pub adapter_context: *mut crate::adapter::AdapterContext,
    pub config: RegistryConfig,
    pub io_queue: WDFQUEUE,
    pub is_initialized: bool,
}

impl Default for DeviceContext {
    fn default() -> Self {
        Self {
            miniport_handle: core::ptr::null_mut(),
            adapter_context: core::ptr::null_mut(),
            config: RegistryConfig::default(),
            io_queue: core::ptr::null_mut(),
            is_initialized: false,
        }
    }
}

/// Event callback for device addition
pub unsafe extern "C" fn EvtDeviceAdd(
    driver: WDFDRIVER,
    device_init: PWDFDEVICE_INIT,
) -> NTSTATUS {
    // Set device type to network device
    WdfDeviceInitSetDeviceType(device_init, FILE_DEVICE_NETWORK);

    // Set device characteristics
    WdfDeviceInitSetCharacteristics(device_init, FILE_DEVICE_SECURE_OPEN, false);

    let mut device_attributes = WDF_OBJECT_ATTRIBUTES::default();
    device_attributes.ContextSizeOverride = core::mem::size_of::<DeviceContext>();

    let mut device: WDFDEVICE = core::ptr::null_mut();
    let mut status = WdfDeviceCreate(
        &mut device_init,
        &mut device_attributes as PWDF_OBJECT_ATTRIBUTES,
        &mut device,
    );

    if status != STATUS_SUCCESS {
        return status;
    }

    // 获取设备上下文并初始化
    let device_context = get_device_context_mut(device);
    if device_context.is_null() {
        return STATUS_UNSUCCESSFUL;
    }

    // 读取注册表配置
    match read_driver_config() {
        Ok(config) => {
            (*device_context).config = config;
        }
        Err(_) => {
            // 使用默认配置
            (*device_context).config = RegistryConfig::default();
        }
    }

    // 创建 I/O 队列
    status = create_io_queue(device, device_context);
    if status != STATUS_SUCCESS {
        return status;
    }

    // Initialize NDIS miniport
    status = initialize_ndis_miniport(device);
    if status != STATUS_SUCCESS {
        return status;
    }

    (*device_context).is_initialized = true;
    STATUS_SUCCESS
}

/// 创建设备 I/O 队列
unsafe fn create_io_queue(
    device: WDFDEVICE,
    device_context: *mut DeviceContext,
) -> NTSTATUS {
    let mut queue_config = WDF_IO_QUEUE_CONFIG::default();
    queue_config.DispatchType = WdfIoQueueDispatchParallel;
    queue_config.EvtIoDeviceControl = Some(EvtIoDeviceControl);
    queue_config.PowerManaged = 0; // FALSE

    let mut queue_attributes = WDF_OBJECT_ATTRIBUTES::default();

    let mut status = WdfIoQueueCreate(
        device,
        &mut queue_config as PWDF_IO_QUEUE_CONFIG,
        &mut queue_attributes as PWDF_OBJECT_ATTRIBUTES,
        &mut (*device_context).io_queue,
    );

    if status != STATUS_SUCCESS {
        return status;
    }

    // 配置设备控制请求分发
    status = WdfDeviceConfigureRequestDispatching(
        device,
        (*device_context).io_queue,
        WdfRequestTypeDeviceControl,
    );

    status
}

/// 获取设备上下文（可变）
pub unsafe fn get_device_context_mut(device: WDFDEVICE) -> *mut DeviceContext {
    // 在实际实现中，这会使用 WDF API 获取设备上下文
    // 当前返回 null，需要实际的 WDF 集成
    core::ptr::null_mut()
}

/// 获取设备上下文（只读）
pub unsafe fn get_device_context(device: WDFDEVICE) -> *const DeviceContext {
    get_device_context_mut(device) as *const DeviceContext
}
