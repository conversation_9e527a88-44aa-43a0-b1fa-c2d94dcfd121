use wdk_sys::{
    NTSTATUS, STATUS_SUCCESS, STATUS_UNSUCCESSFUL,
    WDFDRIVER, WDFDEVICE, PWDFDEVICE_INIT,
    WDF_DEVICE_INIT, WDF_OBJECT_ATTRIBUTES, WDF_NO_OBJECT_ATTRIBUTES,
    WdfDeviceCreate, WdfDeviceInitSetDeviceType, WdfDeviceInitSetCharacteristics,
    FILE_DEVICE_NETWORK, FILE_DEVICE_SECURE_OPEN,
    PWDF_OBJECT_ATTRIBUTES,
};

use crate::ndis::initialize_ndis_miniport;

/// Device context structure
#[repr(C)]
pub struct DeviceContext {
    pub miniport_handle: *mut core::ffi::c_void,
    pub adapter_context: *mut crate::adapter::AdapterContext,
}

impl Default for DeviceContext {
    fn default() -> Self {
        Self {
            miniport_handle: core::ptr::null_mut(),
            adapter_context: core::ptr::null_mut(),
        }
    }
}

/// Event callback for device addition
pub unsafe extern "C" fn EvtDeviceAdd(
    driver: WDFDRIVER,
    device_init: PWDFDEVICE_INIT,
) -> NTSTATUS {
    // Set device type to network device
    WdfDeviceInitSetDeviceType(device_init, FILE_DEVICE_NETWORK);
    
    // Set device characteristics
    WdfDeviceInitSetCharacteristics(device_init, FILE_DEVICE_SECURE_OPEN, false);

    let mut device_attributes = WDF_OBJECT_ATTRIBUTES::default();
    device_attributes.ContextSizeOverride = core::mem::size_of::<DeviceContext>();

    let mut device: WDFDEVICE = core::ptr::null_mut();
    let status = WdfDeviceCreate(
        &mut device_init,
        &mut device_attributes as PWDF_OBJECT_ATTRIBUTES,
        &mut device,
    );

    if status != STATUS_SUCCESS {
        return status;
    }

    // Initialize NDIS miniport
    let init_status = initialize_ndis_miniport(device);
    if init_status != STATUS_SUCCESS {
        return init_status;
    }

    STATUS_SUCCESS
}
