#![no_std]

use wdk_sys::{
    NTSTATUS, PDRIVER_OBJECT, PCUNICODE_STRING, STATUS_SUCCESS,
    WDFDRIVER, WDFDEVICE, WDF_DRIVER_CONFIG, WDF_OBJECT_ATTRIBUTES,
    WDF_NO_OBJECT_ATTRIBUTES, PWDF_DRIVER_CONFIG, PWDF_OBJECT_ATTRIBUTES,
    WdfDriverCreate, WdfDeviceCreate, WDF_DEVICE_INIT,
};

#[cfg(not(test))]
extern crate wdk_panic;

#[cfg(not(test))]
use wdk_alloc::WdkAllocator;

#[cfg(not(test))]
#[global_allocator]
static GLOBAL_ALLOCATOR: WdkAllocator = WdkAllocator;

mod adapter;
mod device;
mod ndis;
mod ring_buffer;

use device::EvtDeviceAdd;

/// Driver entry point
#[export_name = "DriverEntry"]
pub unsafe extern "system" fn driver_entry(
    driver: PDRIVER_OBJECT,
    registry_path: PCUNICODE_STRING,
) -> NTSTATUS {
    let mut driver_config = WDF_DRIVER_CONFIG::default();
    driver_config.DriverInitFlags = 0;
    driver_config.EvtDriverDeviceAdd = Some(EvtDeviceAdd);

    let mut driver_handle: WDFDRIVER = core::ptr::null_mut();
    let status = WdfDriverCreate(
        driver,
        registry_path,
        WDF_NO_OBJECT_ATTRIBUTES,
        &mut driver_config as PWDF_DRIVER_CONFIG,
        &mut driver_handle,
    );

    if status != STATUS_SUCCESS {
        return status;
    }

    STATUS_SUCCESS
}
