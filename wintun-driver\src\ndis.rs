use wdk_sys::{
    NdisHardwareStatusInitializing, NdisHardwareStatusReady, NdisMediaStateConnected,
    NdisMediaStateDisconnected, NdisRequestQueryInformation, NdisRequestSetInformation, BOOLEAN,
    NDIS_HANDLE, NDIS_HARDWARE_STATUS, NDIS_MEDIA_STATE, NDIS_MINIPORT_DRIVER_CHARACTERISTICS,
    NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2, NDIS_MINIPORT_INIT_PARAMETERS,
    NDIS_OBJECT_TYPE_MINIPORT_DRIVER_CHARACTERISTICS, NDIS_OID_REQUEST, NDIS_REQUEST_TYPE,
    NDIS_SIZEOF_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2, NDIS_STATUS, NDIS_STATUS_FAILURE,
    NDIS_STATUS_NOT_SUPPORTED, NDIS_STATUS_PENDING, NDIS_STATUS_RESOURCES, NDIS_STATUS_SUCCESS,
    NTSTATUS, OID_GEN_CURRENT_LOOKAHEAD, OID_GEN_CURRENT_PACKET_FILTER, OID_GEN_DRIVER_VERSION,
    OID_GEN_HARDWARE_STATUS, OID_GEN_LINK_SPEED, OID_GEN_MAC_OPTIONS, OID_GEN_MAXIMUM_FRAME_SIZE,
    OID_GEN_MAXIMUM_LOOKAHEAD, OID_GEN_MAXIMUM_SEND_PACKETS, OID_GEN_MAXIMUM_TOTAL_SIZE,
    OID_GEN_MEDIA_CONNECT_STATUS, OID_GEN_MEDIA_IN_USE, OID_GEN_MEDIA_SUPPORTED,
    OID_GEN_PROTOCOL_OPTIONS, OID_GEN_RECEIVE_BLOCK_SIZE, OID_GEN_RECEIVE_BUFFER_SPACE,
    OID_GEN_SUPPORTED_LIST, OID_GEN_TRANSMIT_BLOCK_SIZE, OID_GEN_TRANSMIT_BUFFER_SPACE,
    OID_GEN_VENDOR_DESCRIPTION, OID_GEN_VENDOR_ID, PNDIS_MINIPORT_ADAPTER_CONTEXT,
    PNDIS_MINIPORT_INIT_PARAMETERS, PNDIS_OID_REQUEST, PNET_BUFFER, PNET_BUFFER_LIST, PVOID,
    STATUS_PENDING, STATUS_SUCCESS, STATUS_UNSUCCESSFUL, ULONG, WDFDEVICE,
};

use crate::adapter::{set_adapter_attributes, AdapterContext};
use crate::device::DeviceContext;

/// Initialize NDIS miniport for the device
pub unsafe fn initialize_ndis_miniport(device: WDFDEVICE) -> NTSTATUS {
    // Get device context
    let device_context = get_device_context(device);
    if device_context.is_null() {
        return STATUS_UNSUCCESSFUL;
    }

    // Set up miniport characteristics
    let mut miniport_chars = NDIS_MINIPORT_DRIVER_CHARACTERISTICS::default();

    miniport_chars.Header.Type = NDIS_OBJECT_TYPE_MINIPORT_DRIVER_CHARACTERISTICS as u8;
    miniport_chars.Header.Revision = NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2 as u8;
    miniport_chars.Header.Size = NDIS_SIZEOF_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_2 as u16;

    miniport_chars.MajorNdisVersion = 6;
    miniport_chars.MinorNdisVersion = 0;
    miniport_chars.MajorDriverVersion = 1;
    miniport_chars.MinorDriverVersion = 0;

    // Set callback functions
    miniport_chars.InitializeHandlerEx = Some(MiniportInitializeEx);
    miniport_chars.HaltHandlerEx = Some(MiniportHaltEx);
    miniport_chars.UnloadHandler = Some(MiniportDriverUnload);
    miniport_chars.PauseHandler = Some(MiniportPause);
    miniport_chars.RestartHandler = Some(MiniportRestart);
    miniport_chars.OidRequestHandler = Some(MiniportOidRequest);
    miniport_chars.SendNetBufferListsHandler = Some(MiniportSendNetBufferLists);
    miniport_chars.ReturnNetBufferListsHandler = Some(MiniportReturnNetBufferLists);
    miniport_chars.CancelSendHandler = Some(MiniportCancelSend);
    miniport_chars.CheckForHangHandlerEx = Some(MiniportCheckForHangEx);
    miniport_chars.ResetHandlerEx = Some(MiniportResetEx);
    miniport_chars.DevicePnPEventNotifyHandler = Some(MiniportDevicePnPEventNotify);
    miniport_chars.ShutdownHandlerEx = Some(MiniportShutdownEx);
    miniport_chars.CancelOidRequestHandler = Some(MiniportCancelOidRequest);

    // This would register the miniport driver with NDIS
    // For this example, we'll simulate success
    STATUS_SUCCESS
}

/// Get device context from WDFDEVICE
unsafe fn get_device_context(device: WDFDEVICE) -> *mut DeviceContext {
    // This would use WDF APIs to get the device context
    // For this example, we'll return null
    core::ptr::null_mut()
}

// NDIS Miniport Handler Functions

unsafe extern "C" fn MiniportInitializeEx(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    miniport_driver_context: NDIS_HANDLE,
    miniport_init_parameters: PNDIS_MINIPORT_INIT_PARAMETERS,
) -> NDIS_STATUS {
    // Create adapter context
    let adapter_context = match AdapterContext::new(miniport_adapter_context as NDIS_HANDLE) {
        Some(ctx) => Box::into_raw(ctx),
        None => return NDIS_STATUS_SUCCESS, // Should be NDIS_STATUS_RESOURCES
    };

    // Set adapter attributes
    let status = set_adapter_attributes(miniport_adapter_context as NDIS_HANDLE, &*adapter_context);
    if status != NDIS_STATUS_SUCCESS {
        let _ = Box::from_raw(adapter_context);
        return status;
    }

    // Store adapter context
    *(miniport_adapter_context as *mut *mut AdapterContext) = adapter_context;

    NDIS_STATUS_SUCCESS
}

unsafe extern "C" fn MiniportHaltEx(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    halt_action: u32, // NDIS_HALT_ACTION
) {
    let adapter_context = *(miniport_adapter_context as *mut *mut AdapterContext);
    if !adapter_context.is_null() {
        let _ = Box::from_raw(adapter_context);
    }
}

unsafe extern "C" fn MiniportDriverUnload(driver_object: PVOID) {
    // Cleanup driver resources
}

unsafe extern "C" fn MiniportPause(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    miniport_pause_parameters: PVOID,
) -> NDIS_STATUS {
    let adapter_context = *(miniport_adapter_context as *mut *mut AdapterContext);
    if !adapter_context.is_null() {
        (*adapter_context).stop();
    }
    NDIS_STATUS_SUCCESS
}

unsafe extern "C" fn MiniportRestart(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    miniport_restart_parameters: PVOID,
) -> NDIS_STATUS {
    let adapter_context = *(miniport_adapter_context as *mut *mut AdapterContext);
    if !adapter_context.is_null() {
        (*adapter_context).start();
    }
    NDIS_STATUS_SUCCESS
}

// OID 请求处理
unsafe extern "C" fn MiniportOidRequest(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    oid_request: PNDIS_OID_REQUEST,
) -> NDIS_STATUS {
    let adapter_context = *(miniport_adapter_context as *mut *mut AdapterContext);
    if adapter_context.is_null() {
        return NDIS_STATUS_FAILURE;
    }

    let request = &*oid_request;

    match request.RequestType {
        NdisRequestQueryInformation => handle_query_information(adapter_context, oid_request),
        NdisRequestSetInformation => handle_set_information(adapter_context, oid_request),
        _ => NDIS_STATUS_NOT_SUPPORTED,
    }
}

unsafe extern "C" fn MiniportSendNetBufferLists(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    net_buffer_lists: PNET_BUFFER_LIST,
    port_number: u32,
    send_flags: ULONG,
) {
    let adapter_context = *(miniport_adapter_context as *mut *mut AdapterContext);
    if adapter_context.is_null() {
        return;
    }

    let mut current_nbl = net_buffer_lists;

    while !current_nbl.is_null() {
        let next_nbl = (*current_nbl).Next;

        // 处理当前 NetBufferList
        let status = process_send_net_buffer_list(adapter_context, current_nbl);

        // 设置完成状态
        (*current_nbl).Status = status;

        current_nbl = next_nbl;
    }

    // 完成发送操作
    // NdisMSendNetBufferListsComplete(
    //     (*adapter_context).miniport_handle,
    //     net_buffer_lists,
    //     send_flags,
    // );
}

unsafe extern "C" fn MiniportReturnNetBufferLists(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    net_buffer_lists: PVOID,
    return_flags: ULONG,
) {
    // Handle returned packets
}

unsafe extern "C" fn MiniportCancelSend(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    cancel_id: PVOID,
) {
    // Handle send cancellation
}

unsafe extern "C" fn MiniportCheckForHangEx(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
) -> BOOLEAN {
    0 // FALSE - adapter is not hung
}

unsafe extern "C" fn MiniportResetEx(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    addressing_reset: *mut BOOLEAN,
) -> NDIS_STATUS {
    *addressing_reset = 0; // FALSE
    NDIS_STATUS_SUCCESS
}

unsafe extern "C" fn MiniportDevicePnPEventNotify(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    net_device_pnp_event: PVOID,
) {
    // Handle PnP events
}

unsafe extern "C" fn MiniportShutdownEx(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    shutdown_action: u32,
) {
    // Handle system shutdown
}

unsafe extern "C" fn MiniportCancelOidRequest(
    miniport_adapter_context: PNDIS_MINIPORT_ADAPTER_CONTEXT,
    request_id: PVOID,
) {
    // Handle OID request cancellation
}

// OID 查询信息处理
unsafe fn handle_query_information(
    adapter_context: *mut AdapterContext,
    oid_request: PNDIS_OID_REQUEST,
) -> NDIS_STATUS {
    let request = &*oid_request;
    let query_info = &request.DATA.QUERY_INFORMATION;
    let oid = query_info.Oid;

    match oid {
        OID_GEN_SUPPORTED_LIST => {
            let supported_oids = get_supported_oids();
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                supported_oids.as_ptr() as *const u8,
                supported_oids.len() * core::mem::size_of::<u32>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        OID_GEN_HARDWARE_STATUS => {
            let status = NdisHardwareStatusReady;
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                &status as *const _ as *const u8,
                core::mem::size_of::<NDIS_HARDWARE_STATUS>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        OID_GEN_MEDIA_SUPPORTED | OID_GEN_MEDIA_IN_USE => {
            let media = 0u32; // NdisMediumIP
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                &media as *const _ as *const u8,
                core::mem::size_of::<u32>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        OID_GEN_MAXIMUM_FRAME_SIZE => {
            let max_frame_size = crate::adapter::TUN_MTU;
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                &max_frame_size as *const _ as *const u8,
                core::mem::size_of::<u32>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        OID_GEN_LINK_SPEED => {
            let link_speed = 1000000000u32; // 1 Gbps
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                &link_speed as *const _ as *const u8,
                core::mem::size_of::<u32>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        OID_GEN_MEDIA_CONNECT_STATUS => {
            let connect_status = if (*adapter_context).is_running {
                NdisMediaStateConnected
            } else {
                NdisMediaStateDisconnected
            };
            copy_oid_data(
                query_info.InformationBuffer,
                query_info.InformationBufferLength,
                &connect_status as *const _ as *const u8,
                core::mem::size_of::<NDIS_MEDIA_STATE>(),
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesWritten,
                &mut (*oid_request).DATA.QUERY_INFORMATION.BytesNeeded,
            )
        }

        _ => NDIS_STATUS_NOT_SUPPORTED,
    }
}

// OID 设置信息处理
unsafe fn handle_set_information(
    adapter_context: *mut AdapterContext,
    oid_request: PNDIS_OID_REQUEST,
) -> NDIS_STATUS {
    let request = &*oid_request;
    let set_info = &request.DATA.SET_INFORMATION;
    let oid = set_info.Oid;

    match oid {
        OID_GEN_CURRENT_PACKET_FILTER => {
            // TUN 接口不需要包过滤器
            (*oid_request).DATA.SET_INFORMATION.BytesRead = set_info.InformationBufferLength;
            NDIS_STATUS_SUCCESS
        }

        OID_GEN_CURRENT_LOOKAHEAD => {
            // TUN 接口不需要预读
            (*oid_request).DATA.SET_INFORMATION.BytesRead = set_info.InformationBufferLength;
            NDIS_STATUS_SUCCESS
        }

        _ => NDIS_STATUS_NOT_SUPPORTED,
    }
}

// 获取支持的 OID 列表
fn get_supported_oids() -> &'static [u32] {
    &[
        OID_GEN_SUPPORTED_LIST,
        OID_GEN_HARDWARE_STATUS,
        OID_GEN_MEDIA_SUPPORTED,
        OID_GEN_MEDIA_IN_USE,
        OID_GEN_MAXIMUM_LOOKAHEAD,
        OID_GEN_MAXIMUM_FRAME_SIZE,
        OID_GEN_LINK_SPEED,
        OID_GEN_TRANSMIT_BUFFER_SPACE,
        OID_GEN_RECEIVE_BUFFER_SPACE,
        OID_GEN_TRANSMIT_BLOCK_SIZE,
        OID_GEN_RECEIVE_BLOCK_SIZE,
        OID_GEN_VENDOR_ID,
        OID_GEN_VENDOR_DESCRIPTION,
        OID_GEN_CURRENT_PACKET_FILTER,
        OID_GEN_CURRENT_LOOKAHEAD,
        OID_GEN_DRIVER_VERSION,
        OID_GEN_MAXIMUM_TOTAL_SIZE,
        OID_GEN_PROTOCOL_OPTIONS,
        OID_GEN_MAC_OPTIONS,
        OID_GEN_MEDIA_CONNECT_STATUS,
        OID_GEN_MAXIMUM_SEND_PACKETS,
    ]
}

// 复制 OID 数据的辅助函数
unsafe fn copy_oid_data(
    dest_buffer: PVOID,
    dest_length: ULONG,
    src_data: *const u8,
    src_length: usize,
    bytes_written: *mut ULONG,
    bytes_needed: *mut ULONG,
) -> NDIS_STATUS {
    *bytes_needed = src_length as ULONG;

    if dest_length < src_length as ULONG {
        *bytes_written = 0;
        return NDIS_STATUS_RESOURCES;
    }

    if !dest_buffer.is_null() && !src_data.is_null() {
        core::ptr::copy_nonoverlapping(src_data, dest_buffer as *mut u8, src_length);
    }

    *bytes_written = src_length as ULONG;
    NDIS_STATUS_SUCCESS
}

// 处理发送的 NetBufferList
unsafe fn process_send_net_buffer_list(
    adapter_context: *mut AdapterContext,
    net_buffer_list: PNET_BUFFER_LIST,
) -> NDIS_STATUS {
    if !(*adapter_context).is_running {
        return NDIS_STATUS_FAILURE;
    }

    let mut current_nb = (*net_buffer_list).FirstNetBuffer;

    while !current_nb.is_null() {
        let status = process_send_net_buffer(adapter_context, current_nb);
        if status != NDIS_STATUS_SUCCESS {
            return status;
        }
        current_nb = (*current_nb).Next;
    }

    NDIS_STATUS_SUCCESS
}

// 处理单个 NetBuffer
unsafe fn process_send_net_buffer(
    adapter_context: *mut AdapterContext,
    net_buffer: PNET_BUFFER,
) -> NDIS_STATUS {
    // 获取数据包长度
    let data_length = (*net_buffer).DataLength;
    if data_length == 0 || data_length > crate::adapter::MAX_PACKET_SIZE {
        return NDIS_STATUS_FAILURE;
    }

    // 尝试在发送环形缓冲区中分配空间
    let packet_ptr = (*adapter_context).send_ring.allocate_packet(data_length);
    if packet_ptr.is_none() {
        return NDIS_STATUS_RESOURCES;
    }

    let packet_buffer = packet_ptr.unwrap();

    // 复制数据包数据
    let mut copied_length = 0u32;
    let copy_result = copy_net_buffer_data(
        net_buffer,
        packet_buffer,
        data_length,
        &mut copied_length,
    );

    if copy_result != NDIS_STATUS_SUCCESS || copied_length != data_length {
        return NDIS_STATUS_FAILURE;
    }

    // 在实际实现中，这里会通知用户空间有新数据包
    // 或者直接处理数据包路由

    NDIS_STATUS_SUCCESS
}

// 复制 NetBuffer 数据的辅助函数
unsafe fn copy_net_buffer_data(
    net_buffer: PNET_BUFFER,
    dest_buffer: *mut u8,
    max_length: u32,
    copied_length: *mut u32,
) -> NDIS_STATUS {
    *copied_length = 0;

    // 简化的数据复制实现
    // 在实际实现中需要处理 MDL 链表
    let data_offset = (*net_buffer).DataOffset;
    let data_length = core::cmp::min((*net_buffer).DataLength, max_length);

    // 这里需要实际的 MDL 遍历和数据复制逻辑
    // 当前只是占位符实现

    *copied_length = data_length;
    NDIS_STATUS_SUCCESS
}

// 向上层指示接收到的数据包
unsafe fn indicate_receive_packets(adapter_context: *mut AdapterContext) {
    if !(*adapter_context).is_running {
        return;
    }

    // 从接收环形缓冲区读取数据包
    while let Some((packet_data, packet_size)) = (*adapter_context).receive_ring.get_next_packet() {
        // 创建 NetBufferList 并指示给上层
        // 这需要分配 MDL 和 NetBuffer 结构

        // 简化实现：直接释放数据包
        (*adapter_context).receive_ring.release_packet(packet_size);
    }
}
