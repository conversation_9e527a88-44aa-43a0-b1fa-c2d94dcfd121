use wdk_sys::{
    NTSTATUS, STATUS_SUCCESS, STATUS_UNSUCCESSFUL, STATUS_INSUFFICIENT_RESOURCES,
    HANDLE, UNICODE_STRING, OBJECT_ATTRIBUTES, KEY_READ, KEY_WRITE, KEY_ALL_ACCESS,
    REG_DWORD, REG_SZ, REG_BINARY, ULONG, PVOID,
    ZwOpenKey, ZwCreateKey, ZwSetValueKey, ZwQueryValueKey, ZwClose,
    RtlInitUnicodeString, InitializeObjectAttributes,
    KEY_VALUE_PARTIAL_INFORMATION, PKEY_VALUE_PARTIAL_INFORMATION,
};

/// 注册表配置管理
pub struct RegistryConfig {
    pub adapter_name: [u16; 256],
    pub tunnel_type: [u16; 256],
    pub mtu_size: u32,
    pub ring_capacity: u32,
    pub auto_start: bool,
}

impl Default for RegistryConfig {
    fn default() -> Self {
        Self {
            adapter_name: [0; 256],
            tunnel_type: [0; 256],
            mtu_size: 1500,
            ring_capacity: 0x100000, // 1MB
            auto_start: false,
        }
    }
}

/// 从注册表读取驱动程序配置
pub unsafe fn read_driver_config() -> Result<RegistryConfig, NTSTATUS> {
    let mut config = RegistryConfig::default();
    
    // 打开驱动程序参数注册表键
    let registry_path = "\\Registry\\Machine\\System\\CurrentControlSet\\Services\\WintunDriver\\Parameters";
    let mut registry_path_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut registry_path_unicode, registry_path.as_ptr() as *const u16);
    
    let mut object_attributes = OBJECT_ATTRIBUTES::default();
    InitializeObjectAttributes(
        &mut object_attributes,
        &mut registry_path_unicode,
        0x00000040, // OBJ_CASE_INSENSITIVE
        core::ptr::null_mut(),
        core::ptr::null_mut(),
    );
    
    let mut key_handle: HANDLE = core::ptr::null_mut();
    let status = ZwOpenKey(&mut key_handle, KEY_READ, &mut object_attributes);
    
    if status != STATUS_SUCCESS {
        // 如果键不存在，使用默认配置
        return Ok(config);
    }
    
    // 读取各个配置值
    let _ = read_registry_string(key_handle, "AdapterName", &mut config.adapter_name);
    let _ = read_registry_string(key_handle, "TunnelType", &mut config.tunnel_type);
    let _ = read_registry_dword(key_handle, "MTUSize", &mut config.mtu_size);
    let _ = read_registry_dword(key_handle, "RingCapacity", &mut config.ring_capacity);
    
    let mut auto_start_value = 0u32;
    if read_registry_dword(key_handle, "AutoStart", &mut auto_start_value) == STATUS_SUCCESS {
        config.auto_start = auto_start_value != 0;
    }
    
    ZwClose(key_handle);
    Ok(config)
}

/// 写入驱动程序配置到注册表
pub unsafe fn write_driver_config(config: &RegistryConfig) -> NTSTATUS {
    // 创建或打开驱动程序参数注册表键
    let registry_path = "\\Registry\\Machine\\System\\CurrentControlSet\\Services\\WintunDriver\\Parameters";
    let mut registry_path_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut registry_path_unicode, registry_path.as_ptr() as *const u16);
    
    let mut object_attributes = OBJECT_ATTRIBUTES::default();
    InitializeObjectAttributes(
        &mut object_attributes,
        &mut registry_path_unicode,
        0x00000040, // OBJ_CASE_INSENSITIVE
        core::ptr::null_mut(),
        core::ptr::null_mut(),
    );
    
    let mut key_handle: HANDLE = core::ptr::null_mut();
    let mut disposition = 0u32;
    
    let status = ZwCreateKey(
        &mut key_handle,
        KEY_ALL_ACCESS,
        &mut object_attributes,
        0,
        core::ptr::null_mut(),
        0, // REG_OPTION_NON_VOLATILE
        &mut disposition,
    );
    
    if status != STATUS_SUCCESS {
        return status;
    }
    
    // 写入各个配置值
    let _ = write_registry_string(key_handle, "AdapterName", &config.adapter_name);
    let _ = write_registry_string(key_handle, "TunnelType", &config.tunnel_type);
    let _ = write_registry_dword(key_handle, "MTUSize", config.mtu_size);
    let _ = write_registry_dword(key_handle, "RingCapacity", config.ring_capacity);
    let _ = write_registry_dword(key_handle, "AutoStart", if config.auto_start { 1 } else { 0 });
    
    ZwClose(key_handle);
    STATUS_SUCCESS
}

/// 读取注册表字符串值
unsafe fn read_registry_string(
    key_handle: HANDLE,
    value_name: &str,
    buffer: &mut [u16; 256],
) -> NTSTATUS {
    let mut value_name_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut value_name_unicode, value_name.as_ptr() as *const u16);
    
    let mut result_length = 0u32;
    let buffer_size = 512u32; // 足够存储字符串值
    let mut value_buffer = [0u8; 512];
    
    let status = ZwQueryValueKey(
        key_handle,
        &mut value_name_unicode,
        2, // KeyValuePartialInformation
        value_buffer.as_mut_ptr() as PVOID,
        buffer_size,
        &mut result_length,
    );
    
    if status != STATUS_SUCCESS {
        return status;
    }
    
    let partial_info = value_buffer.as_ptr() as PKEY_VALUE_PARTIAL_INFORMATION;
    if (*partial_info).Type != REG_SZ {
        return STATUS_UNSUCCESSFUL;
    }
    
    let data_ptr = (*partial_info).Data.as_ptr() as *const u16;
    let data_length = (*partial_info).DataLength as usize / 2; // 转换为字符数
    let copy_length = core::cmp::min(data_length, buffer.len() - 1);
    
    core::ptr::copy_nonoverlapping(data_ptr, buffer.as_mut_ptr(), copy_length);
    buffer[copy_length] = 0; // 确保以 null 结尾
    
    STATUS_SUCCESS
}

/// 写入注册表字符串值
unsafe fn write_registry_string(
    key_handle: HANDLE,
    value_name: &str,
    value: &[u16; 256],
) -> NTSTATUS {
    let mut value_name_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut value_name_unicode, value_name.as_ptr() as *const u16);
    
    // 计算字符串长度
    let mut string_length = 0;
    for &ch in value.iter() {
        if ch == 0 {
            break;
        }
        string_length += 1;
    }
    
    let data_size = (string_length + 1) * 2; // 包括 null 终止符
    
    ZwSetValueKey(
        key_handle,
        &mut value_name_unicode,
        0,
        REG_SZ,
        value.as_ptr() as PVOID,
        data_size as u32,
    )
}

/// 读取注册表 DWORD 值
unsafe fn read_registry_dword(
    key_handle: HANDLE,
    value_name: &str,
    value: &mut u32,
) -> NTSTATUS {
    let mut value_name_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut value_name_unicode, value_name.as_ptr() as *const u16);
    
    let mut result_length = 0u32;
    let buffer_size = core::mem::size_of::<KEY_VALUE_PARTIAL_INFORMATION>() + 4;
    let mut value_buffer = [0u8; 32];
    
    let status = ZwQueryValueKey(
        key_handle,
        &mut value_name_unicode,
        2, // KeyValuePartialInformation
        value_buffer.as_mut_ptr() as PVOID,
        buffer_size as u32,
        &mut result_length,
    );
    
    if status != STATUS_SUCCESS {
        return status;
    }
    
    let partial_info = value_buffer.as_ptr() as PKEY_VALUE_PARTIAL_INFORMATION;
    if (*partial_info).Type != REG_DWORD || (*partial_info).DataLength != 4 {
        return STATUS_UNSUCCESSFUL;
    }
    
    *value = *((*partial_info).Data.as_ptr() as *const u32);
    STATUS_SUCCESS
}

/// 写入注册表 DWORD 值
unsafe fn write_registry_dword(
    key_handle: HANDLE,
    value_name: &str,
    value: u32,
) -> NTSTATUS {
    let mut value_name_unicode = UNICODE_STRING::default();
    RtlInitUnicodeString(&mut value_name_unicode, value_name.as_ptr() as *const u16);
    
    ZwSetValueKey(
        key_handle,
        &mut value_name_unicode,
        0,
        REG_DWORD,
        &value as *const u32 as PVOID,
        4,
    )
}

/// 创建适配器特定的注册表配置
pub unsafe fn create_adapter_registry_config(
    adapter_guid: &[u8; 16],
    config: &RegistryConfig,
) -> NTSTATUS {
    // 为特定适配器创建注册表项
    // 路径格式: \Registry\Machine\System\CurrentControlSet\Services\WintunDriver\Adapters\{GUID}
    
    // 这里应该实现适配器特定的注册表配置
    // 当前只返回成功
    STATUS_SUCCESS
}

/// 删除适配器的注册表配置
pub unsafe fn delete_adapter_registry_config(adapter_guid: &[u8; 16]) -> NTSTATUS {
    // 删除适配器特定的注册表项
    // 当前只返回成功
    STATUS_SUCCESS
}
