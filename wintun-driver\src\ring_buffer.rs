use wdk_sys::{
    NTSTATUS, STATUS_SUCCESS, STATUS_INSUFFICIENT_RESOURCES, STATUS_BUFFER_OVERFLOW,
    PVOID, SIZE_T, POOL_TYPE, NonPagedPool,
    ExAllocatePoolWithTag, ExFreePoolWithTag,
};

/// Ring buffer for high-performance packet transfer between kernel and user space
#[repr(C)]
pub struct RingBuffer {
    pub buffer: PVOID,
    pub size: u32,
    pub head: u32,
    pub tail: u32,
    pub capacity: u32,
}

/// Packet header in ring buffer
#[repr(C, packed)]
pub struct PacketHeader {
    pub size: u32,
    pub reserved: u32,
}

impl RingBuffer {
    /// Create a new ring buffer with specified capacity
    pub fn new(capacity: u32) -> Option<Self> {
        // Ensure capacity is power of 2 for efficient modulo operations
        if capacity == 0 || (capacity & (capacity - 1)) != 0 {
            return None;
        }

        unsafe {
            let buffer = ExAllocatePoolWithTag(
                NonPagedPool,
                capacity as SIZE_T,
                u32::from_be_bytes(*b"WTUN"), // Pool tag 'WTUN'
            );

            if buffer.is_null() {
                return None;
            }

            // Zero the buffer
            core::ptr::write_bytes(buffer as *mut u8, 0, capacity as usize);

            Some(Self {
                buffer,
                size: capacity,
                head: 0,
                tail: 0,
                capacity,
            })
        }
    }

    /// Get available space for writing
    pub fn available_space(&self) -> u32 {
        let head = self.head;
        let tail = self.tail;
        
        if head >= tail {
            self.capacity - (head - tail) - 1
        } else {
            tail - head - 1
        }
    }

    /// Get available data for reading
    pub fn available_data(&self) -> u32 {
        let head = self.head;
        let tail = self.tail;
        
        if head >= tail {
            head - tail
        } else {
            self.capacity - (tail - head)
        }
    }

    /// Allocate space for a packet in the ring buffer
    pub unsafe fn allocate_packet(&mut self, packet_size: u32) -> Option<*mut u8> {
        let total_size = core::mem::size_of::<PacketHeader>() as u32 + packet_size;
        
        if self.available_space() < total_size {
            return None;
        }

        let offset = self.head;
        let packet_ptr = (self.buffer as *mut u8).add(offset as usize);
        
        // Write packet header
        let header = PacketHeader {
            size: packet_size,
            reserved: 0,
        };
        core::ptr::write(packet_ptr as *mut PacketHeader, header);
        
        // Update head pointer
        self.head = (self.head + total_size) & (self.capacity - 1);
        
        Some(packet_ptr.add(core::mem::size_of::<PacketHeader>()))
    }

    /// Get next packet from ring buffer
    pub unsafe fn get_next_packet(&mut self) -> Option<(*const u8, u32)> {
        if self.available_data() < core::mem::size_of::<PacketHeader>() as u32 {
            return None;
        }

        let offset = self.tail;
        let header_ptr = (self.buffer as *const u8).add(offset as usize) as *const PacketHeader;
        let header = core::ptr::read(header_ptr);
        
        let total_size = core::mem::size_of::<PacketHeader>() as u32 + header.size;
        
        if self.available_data() < total_size {
            return None;
        }

        let packet_ptr = (self.buffer as *const u8).add(
            (offset + core::mem::size_of::<PacketHeader>() as u32) as usize
        );
        
        Some((packet_ptr, header.size))
    }

    /// Release a packet after processing
    pub fn release_packet(&mut self, packet_size: u32) {
        let total_size = core::mem::size_of::<PacketHeader>() as u32 + packet_size;
        self.tail = (self.tail + total_size) & (self.capacity - 1);
    }

    /// Check if ring buffer is empty
    pub fn is_empty(&self) -> bool {
        self.head == self.tail
    }

    /// Check if ring buffer is full
    pub fn is_full(&self) -> bool {
        self.available_space() == 0
    }
}

impl Drop for RingBuffer {
    fn drop(&mut self) {
        if !self.buffer.is_null() {
            unsafe {
                ExFreePoolWithTag(self.buffer, u32::from_be_bytes(*b"WTUN"));
            }
        }
    }
}
