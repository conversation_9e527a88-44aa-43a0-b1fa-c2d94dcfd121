;
; wintun-driver.inf
;

[Version]
Signature="$WINDOWS NT$"
Class=Net
ClassGuid={4d36e972-e325-11ce-bfc1-08002be10318}
Provider=%ManufacturerName%
CatalogFile=wintun-driver.cat
DriverVer= ; TODO: set DriverVer in stampinf property pages
PnpLockdown=1

[DestinationDirs]
DefaultDestDir = 12
wintun-driver_Device_CoInstaller_CopyFiles = 11

; ================= Class section =====================

[ClassInstall32]
Addreg=SampleClassReg

[SampleClassReg]
HKR,,,0,%ClassName%
HKR,,Icon,,-5

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
wintun-driver.sys  = 1,,
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll=1 ; make sure the number matches with SourceDisksNames

;*****************************************
; Install Section
;*****************************************

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%wintun-driver.DeviceDesc%=wintun-driver_Device, Root\wintun-driver ; TODO: edit hw-id

[wintun-driver_Device.NT]
CopyFiles=Drivers_Dir

[Drivers_Dir]
wintun-driver.sys

;-------------- Service installation
[wintun-driver_Device.NT.Services]
AddService = wintun-driver,%SPSVCINST_ASSOCSERVICE%, wintun-driver_Service_Inst

; -------------- wintun-driver driver install sections
[wintun-driver_Service_Inst]
DisplayName    = %wintun-driver.SVCDESC%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\wintun-driver.sys

;
;--- wintun-driver_Device Coinstaller installation ------
;

[wintun-driver_Device.NT.CoInstallers]
AddReg=wintun-driver_Device_CoInstaller_AddReg
CopyFiles=wintun-driver_Device_CoInstaller_CopyFiles

[wintun-driver_Device_CoInstaller_AddReg]
HKR,,CoInstallers32,0x00010000, "WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll,WdfCoInstaller"

[wintun-driver_Device_CoInstaller_CopyFiles]
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll

[wintun-driver_Device.NT.Wdf]
KmdfService =  wintun-driver, wintun-driver_wdfsect
[wintun-driver_wdfsect]
KmdfLibraryVersion = $KMDFVERSION$

[Strings]
SPSVCINST_ASSOCSERVICE= 0x00000002
ManufacturerName="Wintun Driver Manufacturer"
ClassName="Network Adapters"
DiskName = "wintun-driver Installation Disk"
wintun-driver.DeviceDesc = "Wintun TUN Driver Device"
wintun-driver.SVCDESC = "Wintun TUN Driver Service"
