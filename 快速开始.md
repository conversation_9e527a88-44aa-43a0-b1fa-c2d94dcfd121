# Windows TUN 驱动程序 - 快速开始指南

本指南将帮助您快速上手 Windows TUN 驱动程序项目。

## 🚀 5 分钟快速体验

### 1. 环境准备

确保您的系统满足以下要求：
- Windows 10/11 (推荐)
- Rust 工具链 (最新稳定版)
- 管理员权限

### 2. 克隆项目

```bash
git clone <your-repo-url>
cd tun
```

### 3. 构建项目

```powershell
# 构建用户空间 API
cargo build --release -p wintun-api

# 构建示例程序
cargo build --release -p simple-tun
```

### 4. 运行示例

```powershell
# 查看帮助
cargo run --release -p simple-tun -- --help

# 创建虚拟适配器（模拟）
cargo run --release -p simple-tun -- create --name "MyTun"

# 查看驱动版本
cargo run --release -p simple-tun -- version
```

## 📖 详细使用教程

### 创建您的第一个 TUN 适配器

```rust
use wintun_api::{AdapterBuilder, LogLevel};

fn main() -> wintun_api::Result<()> {
    // 设置日志
    unsafe {
        wintun_api::set_logger(Box::new(|level, _timestamp, message| {
            println!("[{:?}] {}", level, message);
        }));
    }

    // 创建适配器
    let adapter = AdapterBuilder::new()
        .name("我的TUN适配器")
        .tunnel_type("Wintun")
        .build()?;

    println!("✅ 成功创建适配器: {}", adapter.name());
    Ok(())
}
```

### 数据包处理示例

```rust
use wintun_api::{Adapter, LogLevel};
use std::time::Duration;

fn packet_demo() -> wintun_api::Result<()> {
    // 打开适配器
    let adapter = Adapter::open("我的TUN适配器")?;
    
    // 启动会话 (256KB 缓冲区)
    let mut session = adapter.start_session(262144)?;
    
    // 发送测试数据包
    let mut send_packet = session.allocate_send_packet(64)?;
    send_packet.data_mut()?.fill(0xAA);
    session.send_packet(send_packet)?;
    println!("📤 发送了测试数据包");
    
    // 接收数据包 (超时 1 秒)
    if session.wait_for_packets(Some(Duration::from_secs(1)))? {
        if let Some(recv_packet) = session.receive_packet()? {
            println!("📥 接收到 {} 字节数据包", recv_packet.size());
            session.release_receive_packet(recv_packet)?;
        }
    }
    
    session.end()?;
    Ok(())
}
```

## 🛠️ 开发环境设置

### 完整开发环境

如果您想开发驱动程序本身，需要额外的工具：

1. **安装 LLVM**：
   ```powershell
   winget install -i LLVM.LLVM --version 17.0.6 --force
   ```

2. **安装 Cargo Make**：
   ```powershell
   cargo install --locked cargo-make --no-default-features --features tls-native
   ```

3. **安装 Windows Driver Kit (WDK)**：
   - 下载并安装最新的 WDK
   - 或使用 Enterprise WDK (eWDK)

### 启用测试签名

开发驱动程序时需要启用测试签名：

```powershell
# 以管理员身份运行
bcdedit /set testsigning on
# 重启计算机
```

## 🧪 测试您的代码

### 运行单元测试

```powershell
# 测试 API
cargo test -p wintun-api

# 测试环形缓冲区
cargo test -p wintun-api ring_buffer::tests

# 运行所有测试
cargo test --workspace --exclude wintun-driver
```

### 性能测试

```powershell
# 发布模式测试（更准确的性能数据）
cargo test --release -p wintun-api
```

## 📊 项目结构概览

```
tun/
├── wintun-driver/          # 内核驱动程序
│   ├── src/
│   │   ├── lib.rs         # 驱动程序入口点
│   │   ├── adapter.rs     # 适配器管理
│   │   ├── device.rs      # 设备管理
│   │   ├── ndis.rs        # NDIS 接口
│   │   └── ring_buffer.rs # 内核环形缓冲区
│   └── wintun-driver.inx  # 驱动程序安装文件
├── wintun-api/             # 用户空间 API
│   └── src/
│       ├── lib.rs         # API 入口点
│       ├── adapter.rs     # 适配器管理
│       ├── session.rs     # 会话管理
│       ├── ring_buffer.rs # 用户空间环形缓冲区
│       └── error.rs       # 错误处理
└── examples/
    └── simple-tun/        # 示例应用程序
        └── src/main.rs    # CLI 工具
```

## 🎯 下一步

1. **阅读完整文档**: 查看 `README_CN.md` 了解详细信息
2. **查看项目状态**: 阅读 `PROJECT_STATUS.md` 了解当前进度
3. **探索代码**: 从 `examples/simple-tun` 开始了解 API 使用
4. **参与开发**: 查看 GitHub Issues 了解待完成的功能

## ❓ 常见问题

**Q: 为什么数据包没有实际传输？**
A: 当前实现是演示版本，驱动程序需要完整的 NDIS 实现才能进行真实的网络传输。

**Q: 如何调试驱动程序？**
A: 使用 WinDbg 连接到内核调试器，或查看 Windows 事件日志。

**Q: 可以在虚拟机中开发吗？**
A: 可以，但建议在物理机上进行最终测试。

**Q: 支持哪些 Windows 版本？**
A: 支持 Windows 7 及以上版本，推荐 Windows 10/11。

## 📞 获取帮助

- 查看项目文档
- 提交 GitHub Issue
- 参考 WireGuard wintun 实现
- 阅读 Windows 驱动程序开发文档

---

🎉 **恭喜！** 您已经完成了快速开始指南。现在可以开始探索和开发您自己的 TUN 驱动程序功能了！
