# Windows TUN 驱动程序部署指南

## 🎯 概述

本指南将帮助您部署和使用完整的 Windows TUN 驱动程序。项目现在包含了所有生产级功能的完整实现。

## 📋 部署前准备

### 系统要求
- Windows 10/11 (推荐) 或 Windows 7/8/8.1
- 管理员权限
- 至少 100MB 可用磁盘空间

### 开发环境要求
- Rust 工具链 (最新稳定版)
- Windows Driver Kit (WDK) - 用于驱动程序构建
- LLVM/Clang - 用于绑定生成
- Visual Studio 或 Build Tools for Visual Studio

## 🔧 构建步骤

### 1. 环境准备

```powershell
# 安装 LLVM
winget install -i LLVM.LLVM --version 17.0.6 --force

# 安装 Cargo Make
cargo install --locked cargo-make --no-default-features --features tls-native

# 克隆项目
git clone <your-repo-url>
cd tun
```

### 2. 构建用户空间组件

```powershell
# 构建 API 和示例程序
cargo build --release --workspace --exclude wintun-driver

# 运行测试
cargo test --workspace --exclude wintun-driver
```

### 3. 构建驱动程序 (需要 WDK 环境)

```powershell
# 打开 eWDK 命令提示符
# 导航到项目目录
cd wintun-driver

# 构建驱动程序
cargo make

# 或者进行语法检查
cargo check
```

## 🚀 部署步骤

### 1. 启用测试签名 (开发环境)

```powershell
# 以管理员身份运行
bcdedit /set testsigning on

# 重启计算机
shutdown /r /t 0
```

### 2. 安装驱动程序

```powershell
# 方法 1: 使用 INF 文件安装 (推荐)
# 右键点击 wintun-driver.inf -> 安装

# 方法 2: 使用 devcon 工具
devcon install wintun-driver.inf Root\WintunDriver

# 方法 3: 使用 pnputil
pnputil /add-driver wintun-driver.inf /install
```

### 3. 验证安装

```powershell
# 检查驱动程序是否加载
sc query WintunDriver

# 检查设备管理器中是否出现设备
devmgmt.msc
```

## 💻 使用指南

### 基本操作

```powershell
# 创建 TUN 适配器
cargo run --release -p simple-tun -- create --name "MyTunAdapter"

# 查看所有适配器
cargo run --release -p simple-tun -- list

# 启动会话 (需要驱动程序加载)
cargo run --release -p simple-tun -- session --name "MyTunAdapter" --capacity 262144

# 查看驱动程序版本
cargo run --release -p simple-tun -- version
```

### 编程接口使用

```rust
use wintun_api::{AdapterBuilder, LogLevel};

// 设置日志
unsafe {
    wintun_api::set_logger(Box::new(|level, _timestamp, message| {
        println!("[{:?}] {}", level, message);
    }));
}

// 创建适配器
let adapter = AdapterBuilder::new()
    .name("MyAdapter")
    .tunnel_type("Wintun")
    .build()?;

// 启动会话
let mut session = adapter.start_session(262144)?;

// 发送数据包
let mut packet = session.allocate_send_packet(64)?;
packet.data_mut()?.fill(0xAA);
session.send_packet(packet)?;

// 接收数据包
if let Some(packet) = session.receive_packet()? {
    println!("Received {} bytes", packet.size());
    session.release_receive_packet(packet)?;
}
```

## 🔍 故障排除

### 常见问题

#### 1. 驱动程序未加载
**症状**: `Error: DriverNotLoaded`
**解决方案**:
```powershell
# 检查驱动程序状态
sc query WintunDriver

# 手动启动驱动程序
sc start WintunDriver

# 检查事件日志
eventvwr.msc
```

#### 2. 测试签名问题
**症状**: 驱动程序安装失败
**解决方案**:
```powershell
# 确认测试签名已启用
bcdedit /enum | findstr testsigning

# 重新启用测试签名
bcdedit /set testsigning on
```

#### 3. 权限问题
**症状**: `Error: AccessDenied`
**解决方案**:
- 以管理员身份运行程序
- 检查用户账户控制 (UAC) 设置

#### 4. 适配器创建失败
**症状**: 适配器创建时出错
**解决方案**:
```powershell
# 检查网络适配器列表
ipconfig /all

# 重置网络配置
netsh int ip reset
netsh winsock reset
```

### 调试技巧

#### 1. 启用详细日志
```rust
// 在代码中启用详细日志
unsafe {
    wintun_api::set_logger(Box::new(|level, timestamp, message| {
        println!("[{:?}] {} - {}", level, timestamp, message);
    }));
}
```

#### 2. 使用 WinDbg 调试驱动程序
```
# 连接到内核调试器
windbg -k net:port=50000,key=*******

# 设置断点
bp wintun_driver!DriverEntry
```

#### 3. 检查系统日志
```powershell
# 查看系统事件日志
Get-WinEvent -LogName System | Where-Object {$_.ProviderName -like "*Wintun*"}

# 查看应用程序日志
Get-WinEvent -LogName Application | Where-Object {$_.ProviderName -like "*Wintun*"}
```

## 📊 性能优化

### 配置建议

#### 1. 环形缓冲区大小
```rust
// 高吞吐量场景
let session = adapter.start_session(1024 * 1024)?; // 1MB

// 低延迟场景
let session = adapter.start_session(256 * 1024)?;  // 256KB

// 内存受限场景
let session = adapter.start_session(128 * 1024)?;  // 128KB
```

#### 2. 批量处理
```rust
// 批量发送多个数据包
let packets = vec![packet1, packet2, packet3];
for packet in packets {
    session.send_packet(packet)?;
}
```

#### 3. 异步处理
```rust
// 使用事件等待
while session.wait_for_packets(Some(Duration::from_millis(100)))? {
    while let Some(packet) = session.receive_packet()? {
        // 处理数据包
        session.release_receive_packet(packet)?;
    }
}
```

## 🔒 安全考虑

### 生产环境部署

#### 1. 代码签名
```powershell
# 使用有效的代码签名证书
signtool sign /f certificate.pfx /p password wintun-driver.sys
```

#### 2. WHQL 认证
- 提交驱动程序到 Microsoft 进行 WHQL 认证
- 获得数字签名以在生产环境中使用

#### 3. 权限最小化
- 仅授予必要的权限
- 使用专用服务账户运行应用程序

## 📈 监控和维护

### 性能监控
```powershell
# 监控网络性能
Get-Counter "\Network Interface(*)\Bytes Total/sec"

# 监控内存使用
Get-Counter "\Memory\Available MBytes"

# 监控 CPU 使用
Get-Counter "\Processor(_Total)\% Processor Time"
```

### 日志管理
```powershell
# 配置日志轮转
# 定期清理旧日志文件
# 监控日志大小
```

## 🎯 下一步

1. **测试验证**: 在测试环境中充分验证功能
2. **性能调优**: 根据实际使用场景优化性能
3. **安全审计**: 进行安全审计和渗透测试
4. **文档完善**: 根据实际部署经验完善文档
5. **生产部署**: 获得必要的认证后部署到生产环境

## 📞 支持

如果遇到问题，请：
1. 查看项目文档和 FAQ
2. 检查 GitHub Issues
3. 提交新的 Issue 并提供详细信息
4. 参考 Windows 驱动程序开发文档

---

**注意**: 这是一个完整的生产级实现，但在生产环境中使用前，建议进行充分的测试和验证。
